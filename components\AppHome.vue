<template>
    <!-- PC端布局 -->
    <div class="flex justify-between w-full h-full">


        <!-- 右边内容栏 -->
        <div class="rightcon" id="homeid" @scroll="hmScroll">
            <div class="flex top_search ">
                <div style="width: 350px;">
                    <img @click="toMore(0)" :src="`/img/index/dlogo.png`"
                        style="cursor: pointer;;width: 100%;height: 100%;object-fit: cover;">
                </div>

                <div class="inputform">
                    <input v-model="inputSearch" class="contactInput w-full md:w-[337px]" type="text"
                        placeholder="请输入搜索关键字" />
                    <div class="search_rinput" @click="toSearch">
                        <img src="/img/index/hmsearch.png" alt="">
                    </div>
                </div>
            </div>

            <div class="flex h-auto home_con">
                <div class="fly">
                    <div class="re_box re_box_left">
                        <div class="swiper" style="border-radius: 12px 0  0 12px;">
                            <swiper class="swiper_s" @swiper="onSwiperImgs" v-bind="swiperOptionsimgs">
                                <swiper-slide v-for="(item, index) in xsImg" :key="index" @click="openUrl(item.url)">
                                    <div class="img_tits">{{ item.title }}</div>
                                    <img :src="item.image" style="width: 100%;height: 100%;object-fit: cover;">
                                </swiper-slide>
                            </swiper>
                            <div class="swiper-pagination"></div>
                        </div>
                    </div>
                </div>
                <div class="fly1">
                    <div class="re_box">
                        <div class="flex justify-between align-center w-full ">
                            <img class="re_top_img" :src="`/img/index/wzjx.png`"></img>
                            <div class="pr-[20px] more_j" @click="toMore(3)">
                                <span>更多</span>
                                <img src="/img/index/more_j.png" alt=""
                                    style="width: 8px;height: 13px;margin-left: 10px;"></img>
                            </div>
                        </div>
                        <div class="re_top_img_line"></div>
                        <div class="re_box_item" v-for="(item, index) in zmList" :key="index" style="margin-top: 10px;"
                            @click="openUrl(item.id)">
                            <div class="re_title">
                                <div class="flex align-center">
                                    <div class="dian"></div>
                                    <span class="one-line-ellipsis lx-span">{{ item.title }}</span>
                                </div>
                                <span class="one-line-ellipsis" style="color: #999999;">{{
                                    item.release_time_text.slice(5, 10) }}</span>
                            </div>

                            <!-- <div class="slices_rol"></div> -->
                        </div>
                    </div>
                </div>
            </div>
            <div class="h-auto home_con1" style="margin-top: 30px;">
                <div class="fly1s" style="display: flex;gap: 20px;">
                    <div style="width: 65%;background-color: #ffffff;">
                        <div class="no-wrap"
                            style="padding: 15px;display: flex;justify-content: space-between;align-items: center;">
                            <div class="dddccc">
                                <div v-for="(item, index) in listType" :key="index"
                                    :class="listIndex == item.id ? 't2' : 't1'" @click="changeListIndex(item.id)">
                                    <div>{{ item.name }}</div>
                                    <div v-if="listIndex == item.id" class="t_line"></div>
                                </div>
                            </div>
                            <div class="pr-[20px]" @click="toMore(3)"
                                style="display: flex;align-items: center;cursor: pointer;">
                                <span style="color: #999999;">更多</span>
                                <img src="/img/index/more_j.png" alt=""
                                    style="width: 8px;height: 13px;margin-left: 5px;"></img>
                            </div>
                        </div>
                        <div style="width: 100%;height: 1px;background-color: #EEEEEE;"></div>
                        <div v-for="(item, index) in jcList" :key="index"  @click="openUrl(item.id)">
                            <div class="re_box_item">
                                <div class="re_title">
                                    <div class="flex align-center">
                                        <div class="dian"></div>
                                        <span class="one-line-ellipsis jc-span">{{ item.title }}</span>
                                    </div>
                                    <span class="one-line-ellipsis" style="color: #999999;">{{
                                        item.release_time_text.slice(5, 10) }}</span>
                                </div>
                            </div>
                            <div v-if="index != 6"
                                style="width: 95%;height: 1px;background-color: #EEEEEE;margin: 0 auto;"></div>
                        </div>
                    </div>
                    <div style="width: 35%;background-color: #ffffff;">
                        <div style="width: 100%;height: 5px;background-color: #338CDE;"></div>
                        <div style="display: flex;justify-content: space-between;align-items: center;">
                            <div style="display: flex;align-items: center;gap: 10px;padding:5px 15px;">
                                <div>
                                    <img src="/img/index/gg.png" alt="" style="width: 40px;"></img>
                                </div>
                                <div class="t1">
                                    通知公告
                                </div>
                            </div>
                            <div class="pr-[20px]" @click="toMore(3)"
                                style="display: flex;align-items: center;cursor: pointer;">
                                <span style="color: #999999;">更多</span>
                                <img src="/img/index/more_j.png" alt=""
                                    style="width: 8px;height: 13px;margin-left: 5px;"></img>
                            </div>
                        </div>
                        <div v-for="(item, index) in ptList" :key="index"  @click="openUrl(item.id)">
                            <div class="re_box_item">
                                <div class="re_title">
                                    <div class="flex align-center">
                                        <div class="dian"></div>
                                        <span class="one-line-ellipsis tz-span">{{ item.title }}</span>
                                    </div>
                                    <span class="one-line-ellipsis" style="color: #999999;">
                                        {{ item.release_time_text.slice(5, 10) }}
                                    </span>
                                </div>
                            </div>
                            <div v-if="index != 6"
                                style="width: 95%;height: 1px;background-color: #EEEEEE;margin: 0 auto;"></div>
                        </div>
                    </div>
                </div>
                <div class="fly1s" style="margin-top: 40px;">
                    <div style="display: flex;justify-content: center;align-items: center;padding-bottom: 15px;">
                        <div>
                            <img :src="`/img/index/r.png`" alt=""></img>
                        </div>
                    </div>
                    <div class="qn"
                        style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px; margin-top: 20px;">
                        <div v-for="item in fwImg" @click="openWeb(item)">
                            <img :src="item.image" alt=""></img>
                        </div>
                    </div>
                </div>

            </div>

            <!-- <div class="h-auto home_con1">
                <img :src="`/img/index/gwybst.png`" style="margin: 0 auto;" alt=""></img>
                <div class="flex flex-wrap" style="margin-top: 30px;">
                    <div class="bstbg" v-for="item, i in bstList" :key="i" @click="openUrl(item.id)">
                        <div class="bst_con">
                            <img :src="`/img/index/bst${i + 1}.png`" alt=""
                                style="width: 34px;height: 34px;margin-top: 25px;margin-bottom: 15px;">
                            <div class="bsttitle">
                                {{ item.title }}
                            </div>
                        </div>
                    </div>
                </div>
            </div> -->

            <!-- <div class="flex h-auto home_con2">
                <div class="fly1s">
                    <div class="re_boxs">
                        <div class="flex justify-between align-center w-full ">
                            <div class="flex align-center justify-start">
                                <img class="re_top_img" :src="`/img/index/ptgg.png`"></img>
                                <img class="re_top_img" style="padding: 20px 0;" :src="`/img/index/hlines.png`"></img>
                            </div>
                            <div class="pr-[20px] more_j" @click="toMore">
                                <span>查看更多</span>
                                <img src="/img/index/more_j.png" alt=""
                                    style="width: 8px;height: 13px;margin-left: 10px;"></img>
                            </div>
                        </div>
                        <div class="re_box_item" v-for="(item, index) in ptList" :key="index" style="margin-top: 10px;"
                            @click="openUrl(item.id)">
                            <div class="re_title">
                                <div class="flex align-center">
                                    <div class="dian"></div>
                                    <span class="one-line-ellipsis" style="width:400px;">{{ item.title }}</span>
                                </div>
                                <span class="one-line-ellipsis" style="color: #999999;">{{
                                    item.release_time_text.slice(5,10) }}</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="fly1s">
                    <div class="re_boxs">
                        <div class="flex justify-between align-center w-full ">
                            <div class="flex align-center justify-start">
                                <img class="re_top_img" :src="`/img/index/jcdt.png`"></img>
                                <img class="re_top_img" style="padding: 20px 0;" :src="`/img/index/hlines.png`"></img>
                            </div>
                            <div class="pr-[20px] more_j" @click="toMore">
                                <span>查看更多</span>
                                <img src="/img/index/more_j.png" alt=""
                                    style="width: 8px;height: 13px;margin-left: 10px;"></img>
                            </div>
                        </div>
                        <div class="re_box_item" v-for="(item, index) in jcList" :key="index" style="margin-top: 10px;"
                            @click="openUrl(item.id)">
                            <div class="re_title">
                                <div class="flex align-center">
                                    <div class="dian"></div>
                                    <span class="one-line-ellipsis" style="width:400px;">{{ item.title }}</span>
                                </div>
                                <span class="one-line-ellipsis" style="color: #999999;">{{
                                    item.release_time_text.slice(5,10) }}</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="fly1s">
                    <div class="re_boxs">
                        <div class="flex justify-between align-center w-full ">
                            <div class="flex align-center justify-start">
                                <img class="re_top_img" :src="`/img/index/qcly.png`"></img>
                                <img class="re_top_img" style="padding: 20px 0;" :src="`/img/index/hlines.png`"></img>
                            </div>
                            <div class="pr-[20px] more_j" @click="toMore">
                                <span>查看更多</span>
                                <img src="/img/index/more_j.png" alt=""
                                    style="width: 8px;height: 13px;margin-left: 10px;"></img>
                            </div>
                        </div>
                        <div class="re_box_item" v-for="(item, index) in qcList" :key="index" style="margin-top: 10px;"
                            @click="openUrl(item.id)">
                            <div class="re_title">
                                <div class="flex align-center">
                                    <div class="dian"></div>
                                    <span class="one-line-ellipsis" style="width:400px;">{{ item.title }}</span>
                                </div>
                                <span class="one-line-ellipsis" style="color: #999999;">{{
                                    item.release_time_text.slice(5,10) }}</span>
                            </div>

                        </div>
                    </div>
                </div>
                <div class="fly1s">
                    <div class="re_boxs">
                        <div class="flex justify-between align-center w-full ">
                            <div class="flex align-center justify-start">
                                <img class="re_top_img" :src="`/img/index/mtjj.png`"></img>
                                <img class="re_top_img" style="padding: 20px 0;" :src="`/img/index/hlines.png`"></img>
                            </div>
                            <div class="pr-[20px] more_j" @click="toMore">
                                <span>查看更多</span>
                                <img src="/img/index/more_j.png" alt=""
                                    style="width: 8px;height: 13px;margin-left: 10px;"></img>
                            </div>
                        </div>
                        <div class="re_box_item" v-for="(item, index) in mtList" :key="index" style="margin-top: 10px;"
                            @click="openUrl(item.id)">
                            <div class="re_title">
                                <div class="flex align-center">
                                    <div class="dian"></div>
                                    <span class="one-line-ellipsis" style="width:400px;">{{ item.title }}</span>
                                </div>
                                <span class="one-line-ellipsis" style="color: #999999;">{{
                                    item.release_time_text.slice(5,10) }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div> -->

            <AppFooter></AppFooter>


            <!-- 底部鼠标 -->
            <div class="mouse_img">
                <img :src="`/img/index/hmouse.png`" alt="">
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import $api from '@/service/webRequest'
import { defineEmits } from 'vue'
import { Swiper, SwiperSlide } from 'swiper/vue'
import emitter from '@/plugins/emitter'
import 'swiper/css'
import 'swiper/css/autoplay'
import "swiper/css/pagination";
import { Autoplay, Navigation, Pagination, Scrollbar, A11y, EffectCoverflow, EffectFade, Mousewheel } from "swiper/modules";
const inputSearch = ref('');

const xsImg = ref([]);
const listType = ref([]);
const listIndex = ref(0);
const detailId = ref(null);

const emit = defineEmits(['toSwpe'])
const swiperOptionsimgs = {
    autoplay: {
        delay: 5000,
        disableOnInteraction: false,
    },
    direction: 'horizontal',
    slidesPerView: 1,
    speed: 500,
    // effect: 'fade',
    loop: true,
    modules: [Autoplay, Pagination],
    pagination: {
        el: '.swiper-pagination'
    },
}
const hmScroll = () => {
    let home = document.getElementById('homeid');
    if (home.scrollTop + home.clientHeight >= home.scrollHeight) {
        emit('toSwpe', 2);
    }
    if (home.scrollTop == 0) {
        emit('toSwpe', 0);
    }
}
const toSearch = () => {
    // emit('toSwpe',9);
    // emitter.emit('inputSea', {
    //     keywords:inputSearch.value,
    //     type: 1,
    // });
    window.open(`/search_info?type=1&keywords=${inputSearch.value}`)
}

const openWeb = (item) => {
    //out 外链   in内部
    if (item.type == 'out') {
        window.open(item.url);
    } else {
        window.open(`/info/${item.url}?type=1`)
    }
    console.log(item)
}

// 查看更多
const toMore = (index: number) => {
    emit('toSwpe', index);
}
const openUrl = (id: number) => {
    window.open(`/info/${id}?type=1`)
}
//跳转详情
// const gotoDetail = (id: number) => {
//      detailId.value = {
//         id: id,
//         type: '1'
//     };
//     localStorage.setItem('detailId', JSON.stringify(detailId.value));
//     emit('toSwpe',6)
//     emitter.emit('detailId', detailId.value);
// }
let newsSwiper: any = null
const onSwiperImgs = (swiper: any) => {
    newsSwiper = swiper
    // swiper.on('slideChange', () => {
    // 	// 更新当前活动幻灯片索引

    // })
}
//文章精选
const zmList = ref([]);

//国务院百事通
const ptList = ref([]);
const jcList = ref([]);
const qcList = ref([]);
const mtList = ref([]);

//新闻分类
const getNewstypeList = async () => {
    let ptggid = null, jcdtid = null, qclyid = null, mtjjid = null;    // 分类id 
    const res = await $api.get('/api/home.news/cate?home=1')
    var list = res.data.data.list;
    listIndex.value = list[0].id;
    listType.value = list;
    list.map((item: any) => {
        if (item.name == '通知公告') {
            ptggid = item.id;
        }
        // if (item.name == '基层动态') {
        //     jcdtid = item.id;
        // }

        // if (item.name == '青春洛阳') {
        //     qclyid = item.id;
        // }
        // if (item.name == '媒体聚焦') {
        //     mtjjid = item.id;
        // }
    });
    //去除平台公告
    listType.value = listType.value.filter((item: any) => item.name != '平台公告');
    console.log(listType.value);
    changeListIndex(listIndex.value);
    // xsId.value = listType.value[0].id;
    //逐梦新声列表 //万众瞩目列表
    // const ress = await $api.get(`/api/home.news/index?cate_id=${xsId.value}&page=1&limit=4`)
    // xsList.value = ress.data.data.list;

    //精选文章
    const ress1 = await $api.get(`/api/home.news/index?page=1&limit=5&fine=1`)
    zmList.value = ress1.data.data.list;

    // //平台公告
    const ress2 = await $api.get(`/api/home.news/index?cate_id=${ptggid}&page=1&limit=5`)
    ptList.value = ress2.data.data.list;
    // //基层动态
    // const ress3 = await $api.get(`/api/home.news/index?cate_id=${jcdtid}&page=1&limit=5`)
    // jcList.value = ress3.data.data.list;
    // //青春洛阳
    // const ress4 = await $api.get(`/api/home.news/index?cate_id=${qclyid}&page=1&limit=5`)
    // qcList.value = ress4.data.data.list;
    // //媒体聚焦
    // const ress5 = await $api.get(`/api/home.news/index?cate_id=${mtjjid}&page=1&limit=5`)
    // mtList.value = ress5.data.data.list;
}

//逐梦新声轮播图
const getxsImg = async () => {
    const res = await $api.get('/api/index/images?page=1&limit=9&status=1')
    xsImg.value = res.data.data.list;
}
const fwImg = ref([]);
//服务青年轮播图
const getFwImg = async () => {
    const res = await $api.get('/api/index/images?page=1&limit=9&status=2')
    fwImg.value = res.data.data.list;
}
onMounted(() => {
    window.addEventListener('scroll', hmScroll);
    getxsImg();
    getFwImg();
    getNewstypeList();
})
const changeListIndex = async (id: number) => {
    listIndex.value = id;
    const ress2 = await $api.get(`/api/home.news/index?cate_id=${id}&page=1&limit=7`)
    jcList.value = ress2.data.data.list;
}
</script>

<style lang="scss" scoped>
@import '@/assets/index.scss';


/* pc端样式 */

.swiper {

    .swiper-pagination {
        text-align: right;
        padding-right: 10px;
        bottom: 0;
        height: 38px;
        line-height: 38px;
        --swiper-pagination-color: #ffffff;
        --swiper-pagination-bullet-inactive-color: rgb(255, 255, 255, 0.5);
        --swiper-pagination-bullet-inactive-opacity: 1
            /* 两种都可以 */
    }

    .img_tits {
        font-family: Microsoft YaHei UI;
        font-weight: bold;
        font-size: 18px;
        color: #FFFEFE;
        position: absolute;
        bottom: 0;
        left: 0;
        background: rgba(0, 0, 0, 0.44);
        width: 100%;
        height: 38px;
        line-height: 38px;
        padding-left: 10px;
    }
}



.rightcon {
    width: 100%;
    height: 100%;
    background-image: url('/img/index/rightcons.png');
    background-size: 100% 100%;
    overflow-y: auto;
    overflow-x: hidden;
}

.top_search {
    margin: 0 auto;
    width: 1200px;
    margin-left: 400px;
    margin-top: 50px;
    height: 70px;
    align-items: center;
    justify-content: space-between;
}

.inputform {
    position: relative;
    // margin-right: 319px;
}

.contactInput {
    width: 337px;
    height: 50px;
    background: #ffffff73;
    border-radius: 8px;
    border: 1px solid #DEE4E8;
    padding-left: 15px;
}

.contactInput::placeholder {
    color: #999999;
}

.contactInput:focus {
    outline: none;
}

.search_rinput {
    width: 68px;
    height: 50px;
    background: #338CDE;
    border-radius: 0px 8px 8px 0px;
    position: absolute;
    top: 0;
    right: 0;
    display: flex;
    justify-content: center;
    align-items: center;
}

.home_con {
    width: 1200px;
    box-sizing: border-box;
    // margin: 0 auto;
    margin-top: 50px;
    margin-left: 400px;

    .fly {
        width: 50%;

        // padding-left: 80px;
        .swiper_s {
            width: 100%;
            height: 418px;
        }
    }

    .fly1 {
        width: 50%;
        // margin-left: 30px;
    }

    .re_box {
        border-width: 1px;
        border-style: solid;
        border-color: transparent;
        width: 100%;
        height: 418px;
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-start;
        align-content: flex-start;
        // gap: 10px;
        background: linear-gradient(#FFFFFF, #FFFFFF) padding-box,
            linear-gradient(0deg, #338CDE, #ACD7FF) border-box;
        position: relative;
        border-radius: 0px 12px 12px 0px;

        .re_top_img {
            padding: 20px;
        }

        .more_j {
            font-family: Microsoft YaHei UI;
            font-weight: 400;
            font-size: 16px;
            color: #999999;
            display: flex;
            align-items: center;
            cursor: pointer;
        }

        .re_top_img_line {
            width: 559px;
            height: 2px;
            background: #EEEEEE;
            margin-left: 20px;
        }

    }

    .re_box_left {
        border-radius: 12px 0px 0px 12px;
    }
}

.re_box_item {
    width: 100%;
    padding: 15px 20px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .re_title {
        width: 100%;
        font-family: Microsoft YaHei;
        font-weight: 400;
        font-size: 16px;
        color: #323232;
        display: flex;
        align-items: center;
        justify-content: space-between;
        cursor: pointer;

        .dian {
            width: 10px;
            height: 10px;
            background: #3B90DF;
            border-radius: 50%;
            margin-right: 15px;
        }
    }

    // &:hover{
    //     background-image: url('/img/index/listhmbg.png');
    //     background-size: 100% 100%;
    //     cursor: pointer;
    //     .slices_rol{
    //         width: 10px;
    //         height: 88px;
    //         background: #FAA828;
    //         position: absolute;
    //         right: 0;
    //     }
    // }
}

.home_con1 {
    width: 1200px;
    box-sizing: border-box;
    // margin: 0 auto;
    margin-top: 50px;
    margin-left: 400px;

    .bstbg {
        width: 280px;
        height: 150px;
        background-image: url('/img/index/bstbg.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        margin-right: 20px;
        margin-bottom: 20px;
        cursor: pointer;

        .bst_con {
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;

            .bsttitle {
                width: 210px;
                font-family: Microsoft YaHei UI;
                font-weight: bold;
                font-size: 18px;
                color: #FFFFFF;
                line-height: 26px;
                text-align: center;
            }

            &:hover {
                .bsttitle {
                    color: #FFE7B8;
                }

            }
        }
    }
}

.home_con2 {
    width: 1200px;
    box-sizing: border-box;
    // margin: 0 auto;
    // margin-top: 10px;
    margin-left: 400px;
    flex-wrap: wrap;

    .fly1s {
        width: 580px;
        margin-right: 20px;
        margin-bottom: 20px;
    }

    .re_boxs {
        width: 100%;
        height: 406px;
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-start;
        align-content: flex-start;
        // gap: 10px;
        background: #FFFFFF;
        position: relative;
        border-radius: 12px;

        .re_top_img {
            padding: 20px;
        }

        .more_j {
            font-family: Microsoft YaHei UI;
            font-weight: 400;
            font-size: 16px;
            color: #999999;
            display: flex;
            align-items: center;
            cursor: pointer;
        }

        .re_box_item {
            width: 100%;
            padding: 20px 20px;
            box-sizing: border-box;
            display: flex;
            justify-content: space-between;
            align-items: center;

            .re_title {
                width: 100%;
                font-family: Microsoft YaHei;
                font-weight: 400;
                font-size: 16px;
                color: #323232;
                display: flex;
                align-items: center;
                justify-content: space-between;
                cursor: pointer;

                .dian {
                    width: 10px;
                    height: 10px;
                    background: #3B90DF;
                    border-radius: 50%;
                    margin-right: 15px;
                }
            }

            // &:hover{
            //     background-image: url('/img/index/listhmbg.png');
            //     background-size: 100% 100%;
            //     cursor: pointer;
            //     .slices_rol{
            //         width: 10px;
            //         height: 88px;
            //         background: #FAA828;
            //         position: absolute;
            //         right: 0;
            //     }
            // }
        }
    }
}

.t1 {
    font-weight: bold;
    font-family: PingFang SC;
    color: #323232;
    font-size: 22px;
    cursor: pointer;
    position: relative;
}

.t1:hover {
    color: #2C8AE0;
}

.t2 {
    font-weight: bold;
    font-family: PingFang SC;
    color: #2C8AE0;
    font-size: 22px;
    cursor: pointer;
    position: relative;
}

.t_line {
    width: 60%;
    height: 5px;
    background-color: #3E92DE;
    margin: 0 auto;
    position: absolute;
    left: 0;
    right: 0;
    bottom: -15px;
}

.qn div {
    cursor: pointer;
}

.tz-span {
    width: 270px;
}

.jc-span {
    width: 550px;
}

.lx-span {
    width: 400px;
}

.dddccc {
    display: flex;
    align-items: center;
    gap: 55px;
}

@media (max-width: 1440px) {

    .tz-span {
        width: 180px;
    }

    .jc-span {
        width: 400px;
    }

    .t1 {
        font-size: 18px;
    }

    .t2 {
        font-size: 18px;
    }

    .lx-span {
        width: 310px;
    }

    .dddccc {
        gap: 28px;
    }

    .top_search {
        width: 920px;
        margin-left: 380px;
    }

    .home_con {
        width: 920px;
        margin-left: 380px;
    }

    .home_con1 {
        width: 920px;
        margin-left: 380px;
    }
}
@media (max-width: 1366px) {
    .top_search {
        width: 860px;
        margin-left: 357px;
    }
 
    .home_con {
        width: 860px;
        margin-left: 357px;
    }

    .home_con1 {
        width: 860px;
        margin-left: 357px;
    }

    .t1 {
        font-size: 16px;
    }

    .t2 {
        font-size: 16px;
    }

    .dddccc {
        gap: 15px;
    }

    .lx-span {
        width: 265px;
    }

    .jc-span {
        width: 375px;
    }

    .tz-span {
        width: 153px;
    }
 
}
@media (max-width: 1280px) {
    .top_search {
        width: 800px;
        margin-left: 357px;
    }
 
    .home_con {
        width: 800px;
        margin-left: 357px;
    }

    .home_con1 {
        width: 800px;
        margin-left: 357px;
    }

    .t1 {
        font-size: 16px;
    }

    .t2 {
        font-size: 16px;
    }

    .dddccc {
        gap: 15px;
    }

    .lx-span {
        width: 265px;
    }

    .jc-span {
        width: 375px;
    }

    .tz-span {
        width: 153px;
    }

}
</style>
