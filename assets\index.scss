.page {
  background-color: transparent;
  position: absolute;
  top: 0;
  width: 100%;
  z-index: 100;
}

.group_1 {
  background-size: 100% 100%;
  width: 100%;
}

.box_1 {
  width: 100%;
  height: 90px;
  justify-content: center;
}

.image_1 {
  width: 154px;
  height: 38px;
}

.text_1 {
  height: 90px;
  line-height: 90px;
  color: rgba(255, 255, 255, 0.5);
  font-size: 16px;
  position: relative;
  overflow: hidden;
  padding: 0px 20px;
  &:hover {
    color: #ffffff;
    height: 100%;
    opacity: 1;
    
    &:before {
      height: 100%;
      opacity: 1;
    }
  }

  &:before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    display: block;
    width: 100%;
    height: 0;
    opacity: 0;
    background: #FC7428;
    transition: all 0.5s ease-in-out;
    z-index: 1;
  }

  a {
    position: relative;
    z-index: 2;
    display: block;
    width: 100%;
    height: 100%;
  }
}

.image-wrapper_1 {
  width: 136px;
  height: 50px;
  margin: 920px 0 39px 892px;
}

.image_2 {
  width: 31px;
  height: 13px;
  margin-top: 17px;
}

.image_3 {
  width: 36px;
  height: 50px;
  margin-left: 20px;
}

.image_4 {
  width: 31px;
  height: 13px;
  margin: 17px 0 0 18px;
}

.group_2 {
  position: relative;
  width: 100%;
  overflow: hidden;
}

.box_2 {
  background-color: rgba(255, 255, 255, 1);
}

.group_3 {
  width: 100%;
  height: 681px;
  background: var(--bg-c) 100% no-repeat;
  background-size: 100% 100%;
  gap: 150px;
}

@media (max-width: 1440px) {
  .group_3 {
    gap: 50px;
  }
}

.text-group_1 {
  width: 256px;
  height: 65px;
}

.text_7 {
  width: 256px;
  height: 32px;
  overflow-wrap: break-word;
  color: rgba(34, 34, 34, 1);
  font-size: 32px;
  font-family: Microsoft YaHei UI-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 32px;
}

.text_8 {
  width: 182px;
  height: 16px;
  overflow-wrap: break-word;
  color: rgba(118, 133, 151, 1);
  font-size: 16px;
  font-family: Microsoft YaHei UI-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 16px;
  margin: 17px 0 0 1px;
}

.text-wrapper_1 {
  width: 436px;
  height: 22px;
  margin: 55px 0 0 1px;
}

.text_9 {
  color: rgba(252, 116, 40, 1);
  font-size: 22px;
  font-weight: 700;
  text-align: left;
  padding-right: 40px;
  cursor: pointer;
}

.text_11 {
  color: rgba(34, 34, 34, 1);
  font-size: 22px;
  text-align: left;
  padding-right: 40px;
  cursor: pointer;
}

.box_3 {
  background-color: rgba(248, 248, 248, 1);
  height: 1px;
  width: 555px;
  margin: 24px 0 0 1px;
}

.box_4 {
  background-color: rgba(252, 116, 40, 1);
  width: 90px;
  height: 1px;
}

.text_12 {
  width: 584px;
  height: auto;
  overflow-wrap: break-word;
  color: rgba(118, 133, 151, 1);
  font-size: 16px;
  font-family: Microsoft YaHei UI-Regular;
  font-weight: 400;
  text-align: left;
  line-height: 24px;
  margin: 20px 0 0 1px;
}

.text-wrapper_2 {
  background-color: rgba(34, 34, 34, 1);
  height: 36px;
  width: 108px;
  margin-top: 20px;
}

.text_13 {
  width: 64px;
  height: 16px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: Microsoft YaHei UI-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 16px;
  margin: 10px 0 0 22px;
}

.section_1 {
  height: 508px;
  background: var(--bg-t2) no-repeat;
  background-size: 100%;
  width: 549px;
  z-index: 10;
}

.image-wrapper_2 {
  background-color: rgba(0, 0, 0, 0.41);
  height: 100%;
  width: 100%;
  position: relative;
  border-radius: 20px;
}

.image_5 {
  width: 70px;
  height: 70px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.image_5s {
  background: var(--m1);
  width: 549px;
  height: 508px;
  z-index: 1;
  position: absolute;
  right: -30px;
  bottom: -30px;
}

.box_5 {
  position: relative;
  width: 100%;
  height: 991px;
  background: var(--bg-b) 100% no-repeat;
  background-size: 100% 100%;
  justify-content: flex-center;
}

.text_14 {
  color: rgba(34, 34, 34, 1);
  font-size: 32px;
  font-weight: 700;
  text-align: center;
  white-space: nowrap;
  margin: 70px 0px 10px 0px;
}

.text_15 {
  color: rgba(118, 133, 151, 1);
  font-size: 16px;
  text-align: center;
  white-space: nowrap;
}

.section_2 {
  background-color: rgba(255, 255, 255, 1);
  height: 440px;
}

.section_2s {
  background-image: linear-gradient(180deg, rgba(108, 169, 255, 0.2) 0%, rgba(255, 255, 255, 0.2) 49%);
  border-radius: 8px 8px 8px 8px;
  height: 745px;
  background-color: #ffffff;
}

.section_2sa {
  background-image: linear-gradient(180deg, rgba(252, 116, 40, 0.2) 0%, rgba(255, 255, 255, 0.2) 49%);
  border-radius: 8px 8px 8px 8px;
  height: 745px;
  background-color: #ffffff;
}

.group_6 {
  height: 154px;
  width: 615px;
  position: relative;
  color: #0256FF;
}

.group_6s {
  color: #FC7428;
  height: 154px;
  width: 615px;
  position: relative;
}

.text-wrapper_3 {
  width: 441px;
  height: 32px;
  margin: 55px 0 0 92px;
}

.text_16 {
  width: 441px;
  height: 32px;
  overflow-wrap: break-word;
  color: rgba(118, 133, 151, 1);
  font-size: 14px;
  font-family: Microsoft YaHei UI-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 32px;
}

.box_6 {
  width: 524px;
  height: 100px;
  padding-top: 50px;
  padding-left: 31px;
}

.image-wrapper_3 {
  height: 44px;
  margin-top: 10px;
  width: 44px;
}

.image-wrapper_3s {
  height: 120px;
  width: 120px;
}

.label_1 {
  width: 44px;
  height: 44px;
}

.label_1s {
  width: 120px;
  height: 120px;
}


.text-group_2 {
  width: 441px;
  height: 63px;
}

.text_17 {
  width: 306px;
  height: 32px;
  overflow-wrap: break-word;
  color: rgba(252, 116, 40, 1);
  font-size: 24px;
  font-family: Microsoft YaHei UI-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 32px;
}

.text_16 {
  width: 441px;
  height: 32px;
  overflow-wrap: break-word;
  color: rgba(118, 133, 151, 1);
  font-size: 14px;
  font-family: Microsoft YaHei UI-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 32px;
  margin-top: 10px;
}

.group_7 {
  width: 500px;
  height: 63px;
  margin: 26px 0 0 30px;
}

.section_3 {
  height: 44px;
  margin-top: 8px;
  width: 44px;
}


.group_9 {
  width: 18px;
  height: 18px;
  margin: 12px 0 0 29px;
}

.text-group_3 {
  width: 427px;
  height: 63px;
  cursor: pointer;
}

.text-group_3:hover .text_18 {
  color: #0256FF !important;
}

.text-group_3:hover .text_19 {
  color: #0256FF !important;
}

.text-group_3_3 {
  width: 427px;
  height: 63px;
  cursor: pointer;
}

.text-group_3_3:hover .text_18 {
  color: #FC7428 !important;
}

.text-group_3_3:hover .text_19 {
  color: #FC7428 !important;
}

.text_18 {
  width: 216px;
  height: 32px;
  overflow-wrap: break-word;
  color: #3D3D3D;
  font-size: 18px;
  font-family: Microsoft YaHei UI-Regular;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 32px;
}

.text_19 {
  width: 456px;
  height: 32px;
  overflow-wrap: break-word;
  color: #768597;
  font-size: 14px;
  font-family: Microsoft YaHei UI-Regular;
  font-weight: 400;
  text-align: left;
  line-height: 32px;
  margin-top: -1px;
}

.group_10 {
  background-color: #EEEEEE;
  width: 524px;
  height: 1px;
  margin-left: 30px;
  margin-top: 30px;
}

.group_10s {
  background-color: #77A5FF;
  width: 524px;
  height: 1px;
  opacity: 0.2;
  margin: 0 30px;
}

.group_11 {
  width: 356px;
  height: 63px;
  margin: 23px 0 0 30px;
}

.section_4 {
  height: 44px;
  margin-top: 10px;
  width: 44px;
}

.image-wrapper_4 {
  border-radius: 10px;
  height: 40px;
  border: 2px solid rgba(61, 61, 61, 1);
  width: 40px;
  margin: 2px 0 0 2px;
}

.label_2 {
  width: 23px;
  height: 23px;
  margin: 9px 0 0 9px;
}

.text-group_4 {
  width: 294px;
  height: 63px;
}

.text_20 {
  width: 216px;
  height: 32px;
  overflow-wrap: break-word;
  color: rgba(61, 61, 61, 1);
  font-size: 18px;
  font-family: Microsoft YaHei UI-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 32px;
}

.text_21 {
  width: 294px;
  height: 32px;
  overflow-wrap: break-word;
  color: rgba(118, 133, 151, 1);
  font-size: 14px;
  font-family: Microsoft YaHei UI-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 32px;
  margin-top: -1px;
}

.group_12 {
  background-color: rgba(248, 248, 248, 1);
  width: 615px;
  height: 1px;
  margin-top: 24px;
}

.group_13 {
  width: 542px;
  height: 63px;
  margin: 22px 0 23px 30px;
}

.image-wrapper_5 {
  height: 48px;
  margin-top: 10px;
  width: 48px;
}

.label_3 {
  width: 48px;
  height: 48px;
  margin: 2px 0 0 3px;
}

.text-group_5 {
  width: 468px;
  height: 63px;
}

.group_5 {
  position: relative;
}

.text_22 {
  width: 169px;
  height: 32px;
  overflow-wrap: break-word;
  color: #3D3D3D;
  font-size: 18px;
  font-family: Microsoft YaHei UI-Regular;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 32px;
}

.text_23 {
  width: 468px;
  height: 32px;
  overflow-wrap: break-word;
  color: #768597;
  font-size: 14px;
  font-family: Microsoft YaHei UI-Regular;
  font-weight: 400;
  text-align: left;
  line-height: 32px;
  margin-top: -1px;
}

.image_6 {
  width: 585px;
  height: 440px;
}


.text_24 {
  overflow-wrap: break-word;
  color: rgba(34, 34, 34, 1);
  font-size: 32px;
  font-weight: 700;
  text-align: center;
  white-space: nowrap;
  margin: 70px 0px 10px 0px;
}

.group_14 {
  width: 65%;
  height: 19px;
  margin: 0 auto;
}

@media screen and (max-width: 1440px) {
  .group_14 {
    width: 95%;
  }
}

.dswper {
  width: 63%;
  margin: 0 auto;
  margin-top: 30px;
  position: relative;
}

@media screen and (max-width: 1440px) {
  .dswper {
    width: 90%;
  }
}

.dswper_left {
  position: absolute;
  top: 50%;
  left: 10%;
  transform: translate(-50%, -50%);
  z-index: 100;
}

.dswper_right {
  position: absolute;
  top: 50%;
  right: 7.5%;
  transform: translate(-50%, -50%);
  z-index: 100;
}

.text_25 {
  overflow-wrap: break-word;
  color: #222222;
  font-size: 16px;
  font-family: Microsoft YaHei UI-Regular;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 16px;
  margin-right: 30px;
  position: relative;
  width: 65px;
  text-align: center;
}

.text_32 {
  width: 64px;
  height: 16px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: Microsoft YaHei UI-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 16px;
  margin-left: 463px;
}

.thumbnail_1 {
  width: 7px;
  height: 6px;
  margin: 13px 0 0 20px;
}

.group_15 {
  background-color: #EEEEEE;
  height: 1px;
  width: 95%;
  margin: 20px auto;
}

.box_8 {
  background-color: rgba(252, 116, 40, 1);
  width: 80px;
  height: 1px;
  margin-left: 2px;
}

.group_16 {
  width: 100%;
  height: 313px;
}

.label_4 {
  width: 47px;
  height: 35px;
  margin-top: 132px;
}


.text-wrapper_4 {
  background-color: rgba(255, 255, 255, 0.12);
  border-radius: 0px 0px 20px 20px;
  height: 75px;
  margin-top: 239px;
  width: 494px;
}

.text_33 {
  width: 201px;
  height: 19px;
  overflow-wrap: break-word;
  color: rgba(61, 61, 61, 1);
  font-size: 18px;
  font-family: Microsoft YaHei UI-Bold;
  font-weight: 700;
  text-align: center;
  white-space: nowrap;
  line-height: 23px;
  margin: 14px 0 0 24px;
}

.group_18 {
  height: 313px;
  background: url(./img/MasterDDSSlicePNG109b3223f127bf28434176713d054319.png) 100% no-repeat;
  background-size: 100% 100%;
  margin-left: 16px;
  width: 494px;
}

.box_9 {
  background-color: rgba(255, 255, 255, 0.12);
  border-radius: 0px 0px 20px 20px;
  width: 494px;
  height: 75px;
  margin-top: 239px;
}

.text_34 {
  width: 201px;
  height: 19px;
  overflow-wrap: break-word;
  color: rgba(61, 61, 61, 1);
  font-size: 18px;
  font-family: Microsoft YaHei UI-Bold;
  font-weight: 700;
  text-align: center;
  white-space: nowrap;
  line-height: 23px;
  margin: 14px 0 0 24px;
}

.text-wrapper_5 {
  background-color: rgba(34, 34, 34, 1);
  height: 40px;
  width: 104px;
  margin: 19px 7px 0 158px;
}

.text_35 {
  width: 64px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: Microsoft YaHei UI-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  margin: 10px 0 0 20px;
}

.label_5 {
  width: 47px;
  height: 35px;
  margin: 132px 0 0 47px;
}

.group_19 {
  height: 313px;
  background: url(./img/MasterDDSSlicePNGd5715a9f6578e6de3d5335d53039a015.png) 100% no-repeat;
  background-size: 100% 100%;
  width: 494px;
  position: absolute;
  left: 453px;
  top: 215px;
}

.text-wrapper_6 {
  background-color: rgba(26, 26, 26, 0.24);
  border-radius: 0px 0px 20px 20px;
  height: 75px;
  margin-top: 239px;
  width: 494px;
}

.text_36 {
  width: 307px;
  height: 19px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 18px;
  font-family: Microsoft YaHei UI-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 23px;
  margin: 14px 0 0 25px;
}

.group_20 {
  height: 313px;
  background: url(./img/MasterDDSSlicePNG1d1d7238d2d121a0244436fe7ce06917.png) 100% no-repeat;
  background-size: 100% 100%;
  width: 494px;
  position: absolute;
  left: 963px;
  top: 215px;
}

.box_10 {
  background-color: rgba(255, 255, 255, 0.12);
  border-radius: 0px 0px 20px 20px;
  width: 494px;
  height: 75px;
  margin-top: 239px;
}

.text_37 {
  width: 201px;
  height: 19px;
  overflow-wrap: break-word;
  color: rgba(61, 61, 61, 1);
  font-size: 18px;
  font-family: Microsoft YaHei UI-Bold;
  font-weight: 700;
  text-align: center;
  white-space: nowrap;
  line-height: 23px;
  margin: 14px 0 0 15px;
}

.text-wrapper_7 {
  background-color: rgba(252, 116, 40, 1);
  height: 40px;
  width: 104px;
  margin: 19px 16px 0 158px;
}

.text_38 {
  width: 64px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: Microsoft YaHei UI-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  margin: 10px 0 0 20px;
}

.text-wrapper_8 {
  background-color: rgba(34, 34, 34, 1);
  height: 40px;
  width: 104px;
  position: absolute;
  left: 1131px;
  top: 494px;
}

.text_39 {
  width: 64px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: Microsoft YaHei UI-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  margin: 10px 0 0 20px;
}

.group_21 {
  height: 380px;
  background: url(./img/MasterDDSSlicePNGb8a7afdd10dd178083a4c9ae8b853b7e.png) 100% no-repeat;
  background-size: 100% 100%;
  width: 600px;
  position: absolute;
  left: 655px;
  top: 181px;
}

.section_5 {
  background-color: rgba(74, 74, 74, 0.21);
  border-radius: 0px 0px 20px 20px;
  height: 90px;
  margin-top: 290px;
  width: 600px;
}

.text-wrapper_9 {
  background-color: rgba(252, 116, 40, 1);
  height: 40px;
  width: 104px;
  margin: 23px 0 0 466px;
}

.text_40 {
  width: 64px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: Microsoft YaHei UI-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  margin: 10px 0 0 20px;
}

.box_11 {
  background-color: rgba(255, 255, 255, 1);
  width: 100%;
  justify-content: flex-center;
  overflow: hidden;
  padding-bottom: 50px;
}

.text-wrapper_10 {
  margin-top: 70px;
  text-align: center;
}

.text_41 {
  color: rgba(34, 34, 34, 1);
  font-size: 32px;
  font-weight: 700;
  line-height: 32px;
}

.text-wrapper_11 {
  margin-top: 10px;
  text-align: center;
}

.text_42 {
  width: 100px;
  height: 16px;
  overflow-wrap: break-word;
  color: rgba(118, 133, 151, 1);
  font-size: 16px;
  font-family: Microsoft YaHei UI-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 16px;
}

.box_12 {
  width: 100%;
  height: 254px;
  margin-top: 37px;
}

.image_7 {
  width: 380px;
  height: 254px;
}

.box_13 {
  // background-color: rgba(248, 249, 251, 1);
  width: 380px;
  height: 254px;
  margin-left: 30px;
}

.text-group_6 {
      width: 340px;
    height: 90px;
    margin: 20px 0 0 18px;
    background: #f2f2f2;
    padding: 10px;
    border-radius: 10px;
}

.text_43 {
  width: 340px;
  height: 32px;
  overflow-wrap: break-word;
  color: rgba(61, 61, 61, 1);
  font-size: 18px;
  font-family: Microsoft YaHei UI-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 32px;
}

.paragraph_1 {
  width: 336px;
  height: 42px;
  overflow-wrap: break-word;
  color: rgba(118, 133, 151, 1);
  font-size: 14px;
  font-family: Microsoft YaHei UI-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 21px;
  margin-top: 16px;
}

.group_22 {
  width: 343px;
  height: 14px;
  margin: 107px 0 23px 15px;
}

.section_6 {
  background: #3B90DF;
  width: 6px;
  height: 6px;
  margin-top: 5px;
  border-radius: 50%;
}

.text_44 {
  width: 56px;
  height: 14px;
  overflow-wrap: break-word;
  color: #3B90DF;
  font-size: 14px;
  font-family: Microsoft YaHei UI-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 14px;
  margin-left: 10px;
}

.text_45 {
  width: 78px;
  height: 14px;
  overflow-wrap: break-word;
  color: rgba(118, 133, 151, 1);
  font-size: 14px;
  font-family: Microsoft YaHei UI-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 14px;
  margin-left: 193px;
}

.image_8 {
  width: 380px;
  height: 254px;
  margin-left: 30px;
}

.box_14 {
  width: 100%;
  height: 254px;
}

.group_23 {
  background-color: rgba(248, 249, 251, 1);
  width: 380px;
  height: 254px;
}

.text-group_7 {
  width: 340px;
  height: 90px;
  margin: 20px 0 0 18px;
}

.text_46 {
  width: 340px;
  height: 32px;
  overflow-wrap: break-word;
  color: rgba(61, 61, 61, 1);
  font-size: 18px;
  font-family: Microsoft YaHei UI-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 32px;
}

.paragraph_2 {
  width: 336px;
  height: 42px;
  overflow-wrap: break-word;
  color: rgba(118, 133, 151, 1);
  font-size: 14px;
  font-family: Microsoft YaHei UI-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 21px;
  margin-top: 16px;
}

.group_24 {
  width: 343px;
  height: 14px;
  margin: 107px 0 23px 15px;
}

.group_25 {
  background-color: rgba(252, 116, 40, 1);
  width: 6px;
  height: 6px;
  margin-top: 5px;
}

.text_47 {
  width: 56px;
  height: 14px;
  overflow-wrap: break-word;
  color: rgba(252, 116, 40, 1);
  font-size: 14px;
  font-family: Microsoft YaHei UI-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 14px;
  margin-left: 10px;
}

.text_48 {
  width: 78px;
  height: 14px;
  overflow-wrap: break-word;
  color: rgba(118, 133, 151, 1);
  font-size: 14px;
  font-family: Microsoft YaHei UI-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 14px;
  margin-left: 193px;
}

.image_9 {
  width: 380px;
  height: 254px;
  margin-left: 30px;
}

.group_26 {
  background-color: rgba(248, 249, 251, 1);
  width: 380px;
  height: 254px;
  margin-left: 30px;
}

.text-group_8 {
  width: 340px;
  height: 90px;
  margin: 20px 0 0 18px;
}

.text_49 {
  width: 340px;
  height: 32px;
  overflow-wrap: break-word;
  color: rgba(61, 61, 61, 1);
  font-size: 18px;
  font-family: Microsoft YaHei UI-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 32px;
}

.paragraph_3 {
  width: 336px;
  height: 42px;
  overflow-wrap: break-word;
  color: rgba(118, 133, 151, 1);
  font-size: 14px;
  font-family: Microsoft YaHei UI-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 21px;
  margin-top: 16px;
}

.box_15 {
  width: 343px;
  height: 14px;
  margin: 107px 0 23px 15px;
}

.group_27 {
  background-color: rgba(252, 116, 40, 1);
  width: 6px;
  height: 6px;
  margin-top: 5px;
}

.text_50 {
  width: 56px;
  height: 14px;
  overflow-wrap: break-word;
  color: rgba(252, 116, 40, 1);
  font-size: 14px;
  font-family: Microsoft YaHei UI-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 14px;
  margin-left: 10px;
}

.text_51 {
  width: 78px;
  height: 14px;
  overflow-wrap: break-word;
  color: rgba(118, 133, 151, 1);
  font-size: 14px;
  font-family: Microsoft YaHei UI-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 14px;
  margin-left: 193px;
}

.box_16 {
  width: 108px;
  height: 36px;
  margin: 0 auto;
  margin-top: 20px;
}

.text-wrapper_12 {
  background-color: rgba(34, 34, 34, 1);
  height: 36px;
  width: 108px;
}

.text_52 {
  width: 64px;
  height: 16px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: Microsoft YaHei UI-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 16px;
  margin: 10px 0 0 22px;
}

.image_10 {
  width: 100px;
  height: 4px;
  margin: 118px 0 0 2455px;
}

.box_17 {
  width: 100%;
  height: 490px;
  background: var(--bg-4) 100% no-repeat;
  background-size: 100% 100%;
  justify-content: flex-center;
}

.box_18 {
  width: 100%;
  height: 465px;
  // background: var(--bg-5) 100% no-repeat;
  background-size: 100% 100%;
  justify-content: flex-center;
}

.text_53 {
  color: rgba(34, 34, 34, 1);
  font-size: 32px;
  font-weight: 700;
  white-space: nowrap;
  line-height: 32px;
  margin-top: 70px;
  text-align: center;
}

.text_54 {
  color: rgba(118, 133, 151, 1);
  font-size: 16px;
  white-space: nowrap;
  line-height: 16px;
  margin-top: 10px;
  text-align: center;
}

.group_28 {
  width: 100%;
  height: 99px;
  margin: 0 auto;
  margin-top: 50px;
}

.text-group_9 {
  width: 251px;
  height: 99px;
}

.text_55 {
  width: 159px;
  height: 50px;
  overflow-wrap: break-word;
  color: rgba(252, 116, 40, 1);
  font-size: 48px;
  font-family: Microsoft YaHei UI-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 48px;
}

.text_56 {
  width: 248px;
  height: 38px;
  overflow-wrap: break-word;
  color: rgba(118, 133, 151, 1);
  font-size: 14px;
  font-family: Microsoft YaHei UI-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 18px;
  margin: 11px 0 0 3px;
}

.text_57 {
  width: 74px;
  height: 19px;
  overflow-wrap: break-word;
  color: rgba(34, 34, 34, 1);
  font-size: 18px;
  font-family: Microsoft YaHei UI-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 18px;
  margin: 27px 0 0 -88px;
}

.section_7 {
  width: 2px;
  height: 62px;
  margin: 13px 0 0 70px;
}

.text-group_10 {
  width: 250px;
  height: 99px;
}

.text_58 {
  width: 99px;
  height: 50px;
  overflow-wrap: break-word;
  color: rgba(252, 116, 40, 1);
  font-size: 48px;
  font-family: Microsoft YaHei UI-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 48px;
}

.text_59 {
  width: 248px;
  height: 38px;
  overflow-wrap: break-word;
  color: rgba(118, 133, 151, 1);
  font-size: 14px;
  font-family: Microsoft YaHei UI-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 18px;
  margin: 11px 0 0 2px;
}

.text_60 {
  width: 74px;
  height: 19px;
  overflow-wrap: break-word;
  color: rgba(34, 34, 34, 1);
  font-size: 18px;
  font-family: Microsoft YaHei UI-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 18px;
  margin: 27px 0 0 -145px;
}

.section_8 {
  width: 2px;
  height: 62px;
  margin: 13px 0 0 98px;
}

.text-group_11 {
  width: 250px;
  height: 99px;
}

.text_61 {
  width: 129px;
  height: 50px;
  overflow-wrap: break-word;
  color: rgba(252, 116, 40, 1);
  font-size: 48px;
  font-family: Microsoft YaHei UI-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 48px;
}

.text_62 {
  width: 248px;
  height: 38px;
  overflow-wrap: break-word;
  color: rgba(118, 133, 151, 1);
  font-size: 14px;
  font-family: Microsoft YaHei UI-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 18px;
  margin: 11px 0 0 2px;
}

.text_63 {
  width: 74px;
  height: 19px;
  overflow-wrap: break-word;
  color: rgba(34, 34, 34, 1);
  font-size: 18px;
  font-family: Microsoft YaHei UI-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 18px;
  margin: 27px 0 0 -116px;
}

.section_9 {
  width: 2px;
  height: 62px;
  margin: 13px 0 0 68px;
}

.text-group_12 {
  width: 250px;
  height: 99px;
}

.text_64 {
  width: 99px;
  height: 50px;
  overflow-wrap: break-word;
  color: rgba(252, 116, 40, 1);
  font-size: 48px;
  font-family: Microsoft YaHei UI-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 48px;
}

.text_65 {
  width: 248px;
  height: 38px;
  overflow-wrap: break-word;
  color: rgba(118, 133, 151, 1);
  font-size: 14px;
  font-family: Microsoft YaHei UI-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 18px;
  margin: 11px 0 0 2px;
}

.text_66 {
  color: #323232;
  font-size: 16px;
  font-family: Microsoft YaHei UI;
  font-weight: 400;
  margin-right: 40px;
}

.group_29 {
  width: 63%;
  margin: 0 auto;
  margin-top: 30px;
}

@media screen and (max-width: 1440px) {
  .group_29 {
    width: 87%;
  }
}

.image-wrapper_7 {
  box-shadow: 0px 4px 10px 0px rgba(228, 228, 228, 1);
  background-color: rgba(255, 255, 255, 1);
  height: 60px;
  width: 175px;
}

.image_11 {
  width: 175px;
  margin: 0 auto;
}

.image_12 {
  width: 175px;
  height: 60px;
  margin-left: 30px;
}

.image_13 {
  width: 175px;
  height: 60px;
  margin-left: 30px;
}

.image_14 {
  width: 176px;
  height: 60px;
  margin-left: 30px;
}

.image_15 {
  width: 176px;
  height: 60px;
  margin-left: 29px;
}

.image_16 {
  width: 176px;
  height: 60px;
  margin-left: 29px;
}

.image-wrapper_8 {
  width: 1200px;
  height: 60px;
  margin: 30px 0 0 359px;
}

.image_17 {
  width: 175px;
  height: 60px;
}

.image_18 {
  width: 175px;
  height: 60px;
  margin-left: 30px;
}

.image_19 {
  width: 176px;
  height: 60px;
  margin-left: 30px;
}

.image_20 {
  width: 176px;
  height: 60px;
  margin-left: 29px;
}

.image_21 {
  width: 176px;
  height: 60px;
  margin-left: 29px;
}

.image_22 {
  width: 175px;
  height: 60px;
  margin-left: 29px;
}

.image-wrapper_9 {
  width: 1200px;
  height: 61px;
  margin: 29px 0 49px 359px;
}

.image_23 {
  width: 175px;
  height: 61px;
}

.image_24 {
  width: 175px;
  height: 61px;
  margin-left: 30px;
}

.image_25 {
  width: 175px;
  height: 61px;
  margin-left: 30px;
}

.image_26 {
  width: 175px;
  height: 61px;
  margin-left: 30px;
}

.image_27 {
  width: 175px;
  height: 60px;
  margin: 1px 0 0 30px;
}

.image_28 {
  width: 175px;
  height: 60px;
  margin: 1px 0 0 30px;
}

.group_30 {
  background-color: transparent;
  width: 1200px;
  margin-left: 400px;
  // height: 300px;
  margin-bottom: 50px;
}


.text-wrapper_13 {
  width: 100%;
  // height: 17px;
  margin-top: 20px;
}

.text_67 {
  color: #323232;
  font-size: 16px;
  font-family: Microsoft YaHei UI;
  font-weight: 400;
  margin-right: 40px;
  &:hover {
    color: #EC0A0A;
  }
}

.text_68 {
  width: 83px;
  height: 16px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 0.5);
  font-size: 16px;
  font-family: Microsoft YaHei UI-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 16px;
  margin-left: 48px;
}

.text_69 {
  width: 83px;
  height: 16px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 0.5);
  font-size: 16px;
  font-family: Microsoft YaHei UI-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 16px;
  margin-left: 46px;
}

.text_70 {
  width: 78px;
  height: 16px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 0.5);
  font-size: 16px;
  font-family: Microsoft YaHei UI-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 16px;
  margin-left: 45px;
}

.text_71 {
  width: 78px;
  height: 16px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 0.5);
  font-size: 16px;
  font-family: Microsoft YaHei UI-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 16px;
  margin-left: 47px;
}

.text_72 {
  width: 64px;
  height: 16px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 0.5);
  font-size: 16px;
  font-family: Microsoft YaHei UI-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 16px;
  margin-left: 47px;
}

.text_73 {
  width: 188px;
  height: 16px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 0.5);
  font-size: 16px;
  font-family: Microsoft YaHei UI-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 16px;
  margin: 1px 0 0 328px;
}

.group_31 {
  margin-top: 32px;
  width: 1200px;
  height: 1px;
  background: #D2D2D2;
  border-radius: 1px;
}

@media screen and (max-width: 1440px) {
  .group_31 {
    width: 94%;
  }
}

.group_32 {
  width: 100%;
  height: 125px;
  // margin: 29px 0 44px 0px;
  align-items: center;
  margin-top: 20px;
}

.text-wrapper_14 {
  // width: 545px;
  // height: 108px;
  height: 100%;
}

.text_74 {
  // width: 545px;
  color: #323232;
  font-size: 16px;
  font-family: Source Han Serif CN;
  font-weight: 400;
  margin-bottom: 10px;
}

.text_75 {
  width: 180px;
  height: 16px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 0.5);
  font-size: 12px;
  font-family: Microsoft YaHei UI-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 16px;
  margin-top: 14px;
}

.text_76 {
  width: 421px;
  height: 16px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 0.5);
  font-size: 12px;
  font-family: Microsoft YaHei UI-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 16px;
  margin-top: 15px;
}

.text_77 {
  width: 273px;
  height: 16px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 0.5);
  font-size: 12px;
  font-family: Microsoft YaHei UI-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 16px;
  margin-top: 15px;
}

.image_29 {
  width: 56px;
  height: 68px;
  margin-right: 50px;
}

.image_30 {
  width: 92px;
  height: 92px;
  margin-top: 1px;
  border-radius: 8px;
}

body * {
  box-sizing: border-box;
  flex-shrink: 0;
}

body {
  font-family: PingFangSC-Regular, Roboto, Helvetica Neue, Helvetica, Tahoma,
    Arial, PingFang SC-Light, Microsoft YaHei;
}

input {
  background-color: transparent;
  border: 0;
}

button {
  margin: 0;
  padding: 0;
  border: 1px solid transparent;
  outline: none;
  background-color: transparent;
}

button:active {
  opacity: 0.6;
}

.flex-col {
  display: flex;
  flex-direction: column;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.justify-start {
  display: flex;
  justify-content: flex-start;
}

.justify-center {
  display: flex;
  justify-content: center;
}

.justify-end {
  display: flex;
  justify-content: flex-end;
}

.justify-evenly {
  display: flex;
  justify-content: space-evenly;
}

.justify-around {
  display: flex;
  justify-content: space-around;
}

.justify-between {
  display: flex;
  justify-content: space-between;
}

.align-start {
  display: flex;
  align-items: flex-start;
}

.align-center {
  display: flex;
  align-items: center;
}

.align-end {
  display: flex;
  align-items: flex-end;
}

.three-line-ellipsis {
  display: -webkit-box;
  /* 使用弹性盒模型 */
  -webkit-box-orient: vertical;
  /* 内容垂直排列 */
  -webkit-line-clamp: 3;
  /* 限制显示的行数 */
  overflow: hidden;
  /* 隐藏超出部分 */
  text-overflow: ellipsis;
  /* 超出部分显示省略号 */
  word-break: break-all;
  /* 允许单词内换行（可选） */
}

.one-line-ellipsis {
  display: -webkit-box;
  /* 使用弹性盒模型 */
  -webkit-box-orient: vertical;
  /* 内容垂直排列 */
  -webkit-line-clamp: 1;
  /* 限制显示的行数 */
  overflow: hidden;
  /* 隐藏超出部分 */
  text-overflow: ellipsis;
  /* 超出部分显示省略号 */
  word-break: break-all;
  /* 允许单词内换行（可选） */
}

.two-line-ellipsis {
  display: -webkit-box;
  /* 使用弹性盒模型 */
  -webkit-box-orient: vertical;
  /* 内容垂直排列 */
  -webkit-line-clamp: 2;
  /* 限制显示的行数 */
  overflow: hidden;
  /* 隐藏超出部分 */
  text-overflow: ellipsis;
  /* 超出部分显示省略号 */
  word-break: break-all;
  /* 允许单词内换行（可选） */
}