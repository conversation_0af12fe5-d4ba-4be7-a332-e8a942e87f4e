<template>
    <!-- PC端布局 -->
    <div class="flex justify-between w-full h-full">
        <!-- 左边导航栏 -->
        <AppHeader></AppHeader>
        <!-- 右边内容栏 -->
        <div class="rightcon" @scroll="hmScroll" id="tjright">
            <div class="flex top_search">
                <div style="width: 350px;">
                    <img :src="`/img/index/dlogo.png`" alt="" style="width: 100%; height: 100%; object-fit: cover" />
                </div>
                <div class="inputform">
                    <input v-model="keywords" class="contactInput w-full md:w-[337px]" type="text"
                        placeholder="请输入搜索关键字" />
                    <div class="search_rinput" @click="getNewsdetail()">
                        <img src="/img/index/hmsearch.png" alt="" />
                    </div>
                </div>
            </div>
    
            <div class="News_box h-auto">
                <div class="lef_box">
                    <div class="re_box">
                        <div class="re_box_item"  >
                            <div class='re_mian'>找到结果{{Newsdetailist.length}}条</div>
                            <div v-for=" item,index in Newsdetailist" :key="index" @click="gotoDetail(item.id)" style="width: 100%;">
                                    <div class="re_title">{{ item.title }}</div>
                                <!-- <div class="re_content">
                                    {{  }}
                                </div> -->
                                <div class="re_date">发布时间：{{ item.release_time_text }}</div>
                                <div class="re_slice"></div>
                            </div>
                        </div>
                    </div>
                </div>
    
                <div class="rig_box" >
                    <n-tabs :bar-width="0" type="line" 
                    class="custom-tabs" pane-style="background-color: #ffffff;">
                        <n-tab-pane name="oasis" tab="热门推荐" style="padding: 30px;">
                            <div class="flex flex-row" v-for="item,index in remList" :key="index" style="padding-bottom:30px;cursor: pointer;" @click="gotoDetails(item.id)">
                                <div class="number_t" v-if="index == 0 || index == 1 || index == 2">
                                    {{index+1 > 10 ? index + 1 : '0'+(index+1) }}
                                </div>
                                <div class="number_t1" v-else>
                                    <span v-if="index+1 == 6" style="color: #D70E08;">{{index+1 > 10 ? index + 1 : '0'+(index+1) }}</span>
                                    <span v-else>{{index+1 > 10 ? index + 1 : '0'+(index+1) }}</span>
                                </div>
                                <div class="content_t2">
                                    <p class="p1 one-line-ellipsis">{{ item.title }}</p>
                                    <p class="p2 flex justify-between items-center">
                                        <span>{{ item.source }}</span>
                                        <span class="flex items-center">
                                            <img src="/img/views.png" alt="" style="height: 13px;padding-right: 10px;">
                                            {{ item.views }}
                                        </span>
                                    </p>
                                </div>
                            </div>
                        </n-tab-pane>
                        <n-tab-pane name="jay chou" tab="精选文章" style="padding: 30px;">
                         <div class="flex flex-row" v-for="item,index in jinxList" :key="index" style="padding-bottom:30px;cursor: pointer;" @click="gotoDetails(item.id)">
                                <div class="number_t" v-if="index == 0 || index == 1 || index == 2">
                                    {{index+1 > 10 ? index + 1 : '0'+(index+1) }}
                                </div>
                                <div class="number_t1" v-else>
                                    <span v-if="index+1 == 6" style="color: #D70E08;">{{index+1 > 10 ? index + 1 : '0'+(index+1) }}</span>
                                    <span v-else>{{index+1 > 10 ? index + 1 : '0'+(index+1) }}</span>
                                </div>
                                <div class="content_t2">
                                    <p class="p1 one-line-ellipsis">{{ item.title }}</p>
                                    <p class="p2 flex justify-between items-center">
                                        <span>{{ item.source }}</span>
                                        <span class="flex items-center">
                                            <img src="/img/views.png" alt="" style="height: 13px;padding-right: 10px;">
                                            {{ item.views }}
                                        </span>
                                    </p>
                                </div>
                            </div>
                        </n-tab-pane>
                    </n-tabs>
                    
                </div>
            </div>
    
            <!-- 右分享 -->
            <!-- <div style="position: absolute;top: 300px;right:36px;" class="flex flex-col fiximg">
    <img :src="`/img/index/douyin.png`" alt="">
    <img :src="`/img/index/wb.png`" alt="">
    <img :src="`/img/index/wx.png`" alt="">
    <img :src="`/img/index/blbl.png`" alt="">
    </div> -->
            <!-- 底部鼠标 -->
            <!-- <div style="position: absolute; bottom: 30px; right: 30px">
                <img :src="`/img/index/hmouse.png`" alt="" />
            </div> -->
        </div>
    </div>
</template>

<script lang="ts" setup>
import { NTabs,NTabPane } from 'naive-ui'
import $api from '@/service/webRequest'
import { defineEmits } from 'vue'
// import emitter from '@/plugins/emitter'
const route = useRoute()
const emit = defineEmits(['toSwpe'])
const page = ref(1);
const jinxList = ref([]);
const remList = ref([]);
const Newsdetailist = ref([]);
const detailId = ref(null);
const datas = ref(null);
const keywords = ref(route.query.keywords)
const types = ref(route.query.type)
const inputSearch=ref('');
// emitter.on('inputSea', (data) => {
//     console.log(data,111111)
//     datas.value = data;
//     getNewsdetail(data);
// });

const topding = ref(false);
const hmScroll = () => {
    let tjright = document.getElementById('tjright');
    if(tjright.scrollTop>159){
        topding.value = true;
    }else{
        topding.value = false;
    }
    
}
//列表
const getjinxList = async () => {
	const res = await $api.post('/api/home.news/index',
        {
            limit:6,
            page:page.value,
            fine:1
        }
    )
	jinxList.value = res.data.data.list;
}
const getremList = async () => {
	const res = await $api.post('/api/home.news/index',
        {
            limit:6,
            page:page.value,
            hot:1
        }
    )
	remList.value = res.data.data.list;
}
//跳转详情
const gotoDetail = (id: number) => {
    // console.log(datas)
    // detailId.value = {
    //     id: id,
    //     type: datas.value.type
    // };
    // localStorage.setItem('detailId', JSON.stringify(detailId.value));
    // emit('toSwpe',6)
    // emitter.emit('detailId', detailId.value);
    window.open(`/info/${id}?type=${types.value}`)
}
//跳转新闻详情
const gotoDetails = (id: number) => {
    // console.log(datas)
    // detailId.value = {
    //     id: id,
    //     type: 1
    // };
    // localStorage.setItem('detailId', JSON.stringify(detailId.value));
    // emit('toSwpe',6)
    // emitter.emit('detailId', detailId.value);
    window.open(`/info/${id}?type=1`)
}

// 新闻/百科/信息公开 搜索
const getNewsdetail = async () => {
    console.log(types.value);
    // 新闻
    if (types.value == 1) {

        const res = await $api.post('/api/home.news/index',
            {
                keywords:keywords.value,
                page:1,
                limit:10
            }
        )
        console.log(res.data.data.list);
        Newsdetailist.value = res.data.data.list;
    }else if(types.value == 2){
        //信息公开
        const res1 = await $api.post('/api/home.information/index',
            {
                keywords:keywords.value,
                page:1,
                limit:30
            }
        )
        Newsdetailist.value = res1.data.data.list;
    }else{
        // 团务百科
        const res2 = await $api.post('/api/home.encyclopedia/index',
            {
                keywords:keywords.value,
                page:1,
                limit:30
            }
        )
        Newsdetailist.value = res2.data.data.list;
    }
	
}
//加载中
onMounted(() => { 
    getjinxList();
    getremList();
    getNewsdetail();
})
</script>

<style lang="scss" scoped>
@import '@/assets/index.scss';

/* pc端样式 */

.rightcon {
    width:100%;
    height:100%;
    background-image: url('/img/index/rightcons.png');
    background-size: 100% 100%;
    overflow-y: auto;
    position: absolute;
}

.top_search {
    margin-top: 50px;
    height: 70px;
    justify-content: space-between;
    align-items: center;
    margin-left: 415px;
    width: 1200px;
}

.inputform {
    position: relative;
    width: 340px;
}

.contactInput {
    width: 337px;
    height: 50px;
    background: #ffffff;
    border-radius: 8px;
    border: 1px solid #dee4e8;
    padding-left: 15px;
}

.contactInput::placeholder {
    color: #999999;
}

.contactInput:focus {
    outline: none;
}

.search_rinput {
    width: 68px;
    height: 50px;
    background: #338CDE;
    border-radius: 0px 8px 8px 0px;
    position: absolute;
    top: 0;
    right: 0;
    display: flex;
    justify-content: center;
    align-items: center;
}

.News_box {
    display: flex;
    justify-content: space-between;
    width: 1300px;
    margin: 0 auto;

    .lef_box {
        width: 835px;
        .re_box {
            width: 100%;
            height: 100%;
            margin: 0 auto;
            margin-top: 40px;
            padding-left: 100px;
            overflow: hidden;
            .re_box_item {
                width: 100%;
                background: #ffffff;
                border: 1px solid #eef7ff;
                padding: 30px;
                box-sizing: border-box;
                display: flex;
                flex-wrap: wrap;
                /* 允许换行 */
                justify-content: flex-start;
                /* 项目左对齐 */
                cursor: pointer;
                .re_slice {
                    // width: 841px;
                    border-bottom: 1px dashed #bbbbbb;
                    margin-top: 20px;
                    margin-bottom: 30px;
                }
                .re_date{
                    width: 100%;
                    font-family: Microsoft YaHei UI;
                    font-weight: 400;
                    font-size: 16px;
                    color: #338CDE;
                }
                .re_mian {
                    width: 248px;
                    font-family: Microsoft YaHei UI;
                    font-weight: 400;
                    font-size: 16px;
                    color: #666666;
                    margin-bottom: 15px;
                }
                .re_rouse{
                    font-family: Microsoft YaHei UI;
                    font-weight: 400;
                    font-size: 16px;
                    color: #999999;
                    width: 100%;
                    margin-top: 10px;
                }
                .re_title {
                    width: 100%;
                    font-family: Microsoft YaHei UI;
                    font-weight: 400;
                    font-size: 18px;
                    color: #393939;
                    margin-bottom: 10px;
                }

                .re_content {
                    width: 100%;
                    height: 100%;
                    font-family: Microsoft YaHei UI;
                    font-weight: 400;
                    font-size: 16px;
                    color: #999999;
                    margin-bottom: 20px;
                }

                
            }
        }
    }

    .rig_box {
        width: 439px;
        height: 100%;
        margin-top: 40px;
        margin-left: 40px;
       .custom-tabs {
        .number_t{
            width: 34px;
            height: 34px;
            background: #FFA234;
            border-radius: 17px;
            font-family: Microsoft YaHei UI;
            font-weight: 400;
            font-size: 18px;
            color: #FFFFFF;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 20px;
        }
        .number_t1{
            width: 34px;
            height: 34px;
            background: #FFFFFF;
            border-radius: 17px;
            font-family: Microsoft YaHei UI;
            font-weight: 400;
            font-size: 18px;
            color: #323232;
            display: flex;
            justify-content: center;
            align-items: end;
            margin-right: 20px;
        }
        .content_t2{
            font-family: Microsoft YaHei UI;
            font-weight: 400;
            width: 80%;
            .p1{
                margin-top: 7px;
                font-size: 18px;
                color: #393939;
            }
            .p2{
                margin-top: 15px;
                font-size: 16px;
                color: #999999;
            }
       }
       }

        :deep(.n-tabs .n-tabs-tab-wrapper){
            width: 215px;
            height: 72px;
            background: #ffffff;
            
            font-size: 18px;
            justify-content: center;
        }
        :deep(.n-tabs .n-tabs-tab .n-tabs-tab__label){
            font-family: Microsoft YaHei UI;
            font-weight: 400;
            font-size: 18px;
        }
        :deep(.n-tabs.n-tabs--line-type .n-tabs-tab:hover, .n-tabs.n-tabs--bar-type .n-tabs-tab:hover){
            color: #348CDE;
        }
        :deep(.n-tabs.n-tabs--line-type .n-tabs-tab.n-tabs-tab--active,.n-tabs.n-tabs--bar-type .n-tabs-tab.n-tabs-tab--active){
            color: #348CDE;
            background: #F1F8FF;
            width: 100%;
            display: flex;
            justify-content: center;
            border-top:solid 2px #378FDF;
        }
        :deep(.n-tabs .n-tabs-tab-pad){
            width: 0;
        }
        
        
    }
    .topding{
        position: fixed;
        top: 0;
        margin-top: 0;
        right: 315px;
        width: 430px;
    }
}
@media (max-width: 1440px) {
    .top_search {
        width: 920px;
        margin-left: 373px;
        
    }
    .News_box{
        width: 1032px;
        margin-left: 270px;
    }
    .rig_box{
        width: 700px;
    }
}
@media (max-width: 1280px) {
    .top_search {
        width: 800px;
        margin-left: 357px;
        
    }
    .News_box{
        width: 900px;
        margin-left: 260px;
    }
    .rig_box{
        width: 340px!important;
    }
    :deep(.n-tabs-tab-wrapper){
        width: 170px!important;
    }
}
</style>
