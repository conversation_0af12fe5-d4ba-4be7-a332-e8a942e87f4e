@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
    --bg-t2: url('https://cdn.web.0rui.cn/img/t2.png');
    --bg-c: url('https://cdn.web.0rui.cn/img/bg-c.png');
    --m1: url('https://cdn.web.0rui.cn/img/m1.png');
    --bg-b: url('https://cdn.web.0rui.cn/img/bg-b.png');
    --bg-4: url('https://cdn.web.0rui.cn/img/bg-4.png');
    --bg-5: url('https://cdn.web.0rui.cn/img/bg-5.png');
    --qall: url('https://cdn.web.0rui.cn/img/qall.png');
    --close: url('https://cdn.web.0rui.cn/img/close.png');
    --t1a: url('https://cdn.web.0rui.cn/img/about/t1a.png');
    --t1b: url('https://cdn.web.0rui.cn/img/about/t1b.png');
    --ruanzhu: url('https://cdn.web.0rui.cn/img/about/ruanzhu.png');
    --allpeople: url('https://cdn.web.0rui.cn/img/allpeople.png');
    --lianxiyp: url('https://cdn.web.0rui.cn/img/lianxiyp.png');
    --bg-s: url('https://cdn.web.0rui.cn/img/customer/bg-s.png');
    --service_023: url('https://cdn.web.0rui.cn/img/service_023.png');
    --service_033: url('https://cdn.web.0rui.cn/img/service_033.png');
    --service_048: url('https://cdn.web.0rui.cn/img/service_048.png');
    --service_033: url('https://cdn.web.0rui.cn/img/service_033.png');
    --service_051: url('https://cdn.web.0rui.cn/img/service_051.png');
    --content_top: url('https://cdn.web.0rui.cn/img/content_top.png');
    --video: url('https://cdn.web.0rui.cn/img/video.png');
    --wechat1: url('https://cdn.web.0rui.cn/img/wechat1.png');
    --newstop: url('https://cdn.web.0rui.cn/img/societyduty/newstop.png');
  }
  //   const cdnUrl = useCdn()

  //   ${cdnUrl}