<template>
    <!-- PC端布局 -->
    <div class="flex justify-between w-full h-auto">
        <!-- 左边导航栏 -->
        <AppHeader></AppHeader>
        <!-- 右边内容栏 -->
        <div class="rightcon" @scroll="hmScroll" id="tjright">
            <div class="flex top_search">
                <div style="width: 350px;">
                    <img :src="`/img/index/dlogo.png`" alt="" style="width: 100%; height: 100%; object-fit: cover" />
                </div>
                <div class="inputform">
                    <input v-model="inputSearch" class="contactInput w-full md:w-[337px]" type="text"
                        placeholder="请输入搜索关键字" />
                    <div class="search_rinput">
                        <img src="/img/index/hmsearch.png" alt="" />
                    </div>
                </div>
            </div>
            <div class="top_bar">
                <div style="background: #ffffff;width: 100%;display: flex;align-items: center;padding: 18px;">
                    <div>
                        <img :src="`/img/key.png`" style="width: 22px;height: 22px;">
                    </div>
                    <div v-if="Newsdetail != null" style="font-size: 18px;color: #323232;margin-left: 10px;">
                        <div class='re_mian'>
                            <span>{{ title_left }}</span>
                            <span> > </span>
                            <span v-if="Newsdetail.cate != null">{{ Newsdetail.cate.name }}</span>
                            <span v-if="Newsdetail.cate != null"> > </span>
                            <span style="color: rgb(59, 144, 223);">文章详情</span></div>
                    </div>
                </div>
            </div>
            <div class="News_box  h-auto">

                <div class="lef_box">

                    <div class="re_box">
                        <div class="re_box_item" v-if="Newsdetail != null && Newsdetail.type == 1">
                            <!-- <div class='re_mian' v-if="Newsdetail.cate != null">{{ title_left + ' > ' +
                                Newsdetail.cate.name + ' > ' + '正文内容' }} </div> -->
                            <div class="re_title">{{ Newsdetail.title }}</div>
                            <div class='flex justify-between re_rouse'>
                                <span>{{ Newsdetail.release_time_text }}</span>
                                <span v-if="Newsdetail.source != '' && Newsdetail.source != null">来源：{{
                                    Newsdetail.source }}</span>
                            </div>
                            <div class="re_slice"></div>
                            <div class="re_file" v-if="Newsdetail.file != '' && Newsdetail.file != null">
                                附件：
                                <a :href="Newsdetail.file" target="_blank" rel="noopener noreferrer"
                                    style="color: #0066cc;">
                                    {{ Newsdetail.attachment.filename }}
                                </a>
                            </div>
                            <div class="re_content" v-html="Newsdetail.content"></div>
                        </div>

                        <div class="re_box_item" v-if="Newsdetail != null && Newsdetail.type == 2">
                            <div class="re_title" style="margin-top: 0;font-size: 20px;font-weight: 600;">{{
                                Newsdetail.title }}</div>
                            <div class='flex justify-between re_rouse' style="margin-bottom: 20px;">
                                <span>{{ Newsdetail.release_time_text }}</span>
                                <span v-if="Newsdetail.source != '' && Newsdetail.source != null">来源：{{
                                    Newsdetail.source }}</span>
                            </div>
                            <embed type="application/pdf" :src="Newsdetail.file" width="100%" height="580px" alt="">
                        </div>

                    </div>
                </div>
                <div class="rig_box" :class="{'topding': topding}">

                    <n-tabs :bar-width="0" type="line" class="custom-tabs" pane-style="background-color: #ffffff;">
                        <n-tab-pane name="oasis" tab="热门推荐" style="padding: 30px;">
                            <div class="flex flex-row" v-for="item, index in remList" :key="index"
                                style="padding-bottom:30px;cursor: pointer;"
                                @click="gotodetail({ id: item.id, type: '1' })">
                                <div :class="index === 0 ? 'number_t' : index === 1 ? 'number_y' : 'number_u'" v-if="index == 0 || index == 1 || index == 2">
                                    {{ index + 1 > 10 ? index + 1 : '0' + (index + 1) }}
                                </div>
                                <div class="number_t1" v-else>
                                    <!-- <span v-if="index + 1 == 6" style="color: #D70E08;">{{ index + 1 > 10 ? index + 1 :
                                        '0' + (index + 1) }}</span> -->
                                    <span>{{ index + 1 > 10 ? index + 1 : '0' + (index + 1) }}</span>
                                </div>
                                <div class="content_t2">
                                    <p class="p1 one-line-ellipsis">{{ item.title }}</p>
                                    <p class="p2 flex justify-between items-center">
                                        <span>{{ item.source }}</span>
                                        <span class="flex items-center">
                                            <img src="/img/views.png" alt="" style="height: 13px;padding-right: 10px;">
                                            {{ item.views }}
                                        </span>
                                    </p>
                                </div>
                            </div>
                        </n-tab-pane>
                        <n-tab-pane name="jay chou" tab="精选文章" style="padding: 30px;">
                            <div class="flex flex-row" v-for="item, index in jinxList" :key="index"
                                style="padding-bottom:30px; cursor: pointer;"
                                @click="gotodetail({ id: item.id, type: '1' })">
                                <div :class="index === 0 ? 'number_t' : index === 1 ? 'number_y' : 'number_u'" v-if="index == 0 || index == 1 || index == 2">
                                    {{ index + 1 > 10 ? index + 1 : '0' + (index + 1) }}
                                </div>
                                <div class="number_t1" v-else>
                                    <!-- <span v-if="index + 1 == 6" style="color: #D70E08;">{{ index + 1 > 10 ? index + 1 :
                                        '0' + (index + 1) }}</span> -->
                                    <span>{{ index + 1 > 10 ? index + 1 : '0' + (index + 1) }}</span>
                                </div>
                                <div class="content_t2">
                                    <p class="p1 one-line-ellipsis">{{ item.title }}</p>
                                    <p class="p2 flex justify-between items-center">
                                        <span>{{ item.source }}</span>
                                        <span class="flex items-center">
                                            <img src="/img/views.png" alt="" style="height: 13px;padding-right: 10px;">
                                            {{ item.views }}
                                        </span>
                                    </p>
                                </div>
                            </div>
                        </n-tab-pane>
                    </n-tabs>

                </div>
            </div>
            <AppFooter></AppFooter>
            <!-- 右分享 -->
            <!-- <div style="position: absolute;top: 300px;right:36px;" class="flex flex-col fiximg">
                    <img :src="`/img/index/douyin.png`" alt="">
                    <img :src="`/img/index/wb.png`" alt="">
                    <img :src="`/img/index/wx.png`" alt="">
                    <img :src="`/img/index/blbl.png`" alt="">
                </div> -->
            <!-- 底部鼠标 -->
            <!-- <div style="position: absolute; bottom: 30px; right: 30px">
                <img :src="`/img/index/hmouse.png`" alt="" />
            </div> -->
        </div>
    </div>
</template>

<script lang="ts" setup>
import { NTabs, NTabPane } from 'naive-ui'
import $api from '@/service/webRequest'
// import emitter from '@/plugins/emitter'
const route = useRoute()
const inputSearch = ref('')
const page = ref(1);
const jinxList = ref([]);
const remList = ref([]);
const topding = ref(false);
const title_left = ref('新闻动态');
const Newsdetail = ref(null);
const ids = ref(route.params.id)
const types = ref(route.query.type)

// emitter.on('detailId', (data) => {
//     console.log(data,111111)
//     getNewsdetail(data);
// });


//精选
const getjinxList = async () => {
    const res = await $api.post('/api/home.news/index',
        {
            limit: 6,
            page: page.value,
            fine: 1
        }
    )
    jinxList.value = res.data.data.list;
}
//热门
const getremList = async () => {
    const res = await $api.post('/api/home.news/index',
        {
            limit: 6,
            page: page.value,
            hot: 1
        }
    )
    remList.value = res.data.data.list;

}
// 新闻/百科/信息公开 详情
const getNewsdetail = async () => {
    // 新闻详情
    if (types.value == 1) {
        title_left.value = '新闻动态';
        const res = await $api.post('/api/home.news/detail',
            {
                id: ids.value
            }
        )
        Newsdetail.value = res.data.data;
    } else if (types.value == 2) {
        title_left.value = '信息公开';
        //信息公开文章详情
        const res1 = await $api.post('/api/home.information/detail',
            {
                id: ids.value
            }
        )
        Newsdetail.value = res1.data.data;
    } else {
        title_left.value = '团务百科';
        // 团务百科文章详情
        const res2 = await $api.post('/api/home.encyclopedia/detail',
            {
                id: ids.value
            }
        )
        Newsdetail.value = res2.data.data;
    }

}
//推荐列表跳转
const gotodetail = async (data) => {
    // 新闻详情
    if (data.type == 1) {
        title_left.value = '新闻动态';
        const res = await $api.post('/api/home.news/detail',
            {
                id: data.id
            }
        )
        Newsdetail.value = res.data.data;
    } else if (data.type == 2) {
        title_left.value = '信息公开';
        //信息公开文章详情
        const res1 = await $api.post('/api/home.information/detail',
            {
                id: data.id
            }
        )
        Newsdetail.value = res1.data.data;
    } else {
        title_left.value = '团务百科';
        // 团务百科文章详情
        const res2 = await $api.post('/api/home.encyclopedia/detail',
            {
                id: data.id
            }
        )
        Newsdetail.value = res2.data.data;
    }
}
const hmScroll = () => {
    let tjright = document.getElementById('tjright');
    if (tjright.scrollTop > 250) {
        topding.value = true;
    } else {
        topding.value = false;
    }

}
//加载中
onMounted(() => {
    getjinxList();
    getremList();
    getNewsdetail();
    window.addEventListener('scroll', hmScroll);
})
</script>

<style lang="scss" scoped>
@import '@/assets/index.scss';

/* pc端样式 */

.rightcon {
    width: 100%;
    height: 100%;
    background-image: url('/img/index/rightcons.png');
    background-size: 100% 100%;
    overflow-y: auto;
    position: absolute;
}

.top_search {
    margin-top: 50px;
    height: 70px;
    justify-content: space-between;
    align-items: center;
    margin-left: 415px;
    width: 1200px;
}

.inputform {
    position: relative;
    width: 340px;
}

.contactInput {
    width: 337px;
    height: 50px;
    background: #ffffff;
    border-radius: 8px;
    border: 1px solid #dee4e8;
    padding-left: 15px;
}

.contactInput::placeholder {
    color: #999999;
}

.contactInput:focus {
    outline: none;
}

.search_rinput {
    width: 68px;
    height: 50px;
    background: #338CDE;
    border-radius: 0px 8px 8px 0px;
    position: absolute;
    top: 0;
    right: 0;
    display: flex;
    justify-content: center;
    align-items: center;
}

.News_box {
    display: flex;
    justify-content: space-between;
    width: 1300px;
    margin: 0 auto;

    .lef_box {
        width: 835px;

        .re_box {
            width: 100%;
            height: 100%;
            margin: 0 auto;
            margin-top: 20px;
            padding-left: 100px;

            .re_box_item {
                width: 100%;
                background: #ffffff;
                border: 1px solid #eef7ff;
                padding: 30px;
                box-sizing: border-box;
                display: flex;
                flex-direction: column;
                min-height: 750px;

                .re_slice {
                    width: 100%;
                    border-bottom: 1px dashed #bbbbbb;
                    margin-top: 20px;
                }

                .re_file {
                    margin-top: 20px;
                    margin-bottom: 10px;
                }

                .re_mian {
                    // width: 248px;
                    font-family: Microsoft YaHei UI;
                    font-weight: 400;
                    font-size: 16px;
                    color: #666666;
                }

                .re_rouse {
                    font-family: Microsoft YaHei UI;
                    font-weight: 400;
                    font-size: 16px;
                    color: #999999;
                    width: 100%;
                    margin-top: 10px;
                }

                .re_title {
                    width: 100%;
                    font-family: Microsoft YaHei UI;
                    font-weight: 500;
                    font-size: 28px;
                    color: #393939;
                }

                .re_content {
                    width: 100%;
                    height: 100%;
                    font-family: Microsoft YaHei UI;
                    font-weight: 400;
                    font-size: 16px;
                    color: #999999;
                    padding: 30px 0;
                }


            }
        }
    }

    .rig_box {
        width: 430px;
        height: 100%;
        margin-top: 20px;
        margin-left: 35px;

        .custom-tabs {
            .number_t {
                width: 34px;
                height: 34px;
                // background: #FFA234;
                background: #E40106;
                border-radius: 17px;
                font-family: Microsoft YaHei UI;
                font-weight: 400;
                font-size: 18px;
                color: #FFFFFF;
                display: flex;
                justify-content: center;
                align-items: center;
                margin-right: 20px;
            }
            .number_y {
                width: 34px;
                height: 34px;
                background: #FFA234;
                border-radius: 17px;
                font-family: Microsoft YaHei UI;
                font-weight: 400;
                font-size: 18px;
                color: #FFFFFF;
                display: flex;
                justify-content: center;
                align-items: center;
                margin-right: 20px;
            }
            .number_u {
                width: 34px;
                height: 34px;
                background: #338CDE;
                border-radius: 17px;
                font-family: Microsoft YaHei UI;
                font-weight: 400;
                font-size: 18px;
                color: #FFFFFF;
                display: flex;
                justify-content: center;
                align-items: center;
                margin-right: 20px;
            }

            .number_t1 {
                width: 34px;
                height: 34px;
                background: #FFFFFF;
                border-radius: 17px;
                font-family: Microsoft YaHei UI;
                font-weight: 400;
                font-size: 18px;
                color: #323232;
                display: flex;
                justify-content: center;
                align-items: end;
                margin-right: 20px;
            }

            .content_t2 {
                font-family: Microsoft YaHei UI;
                font-weight: 400;
                width: 80%;

                .p1 {
                    margin-top: 7px;
                    font-size: 18px;
                    color: #393939;
                }

                .p2 {
                    margin-top: 15px;
                    font-size: 16px;
                    color: #999999;
                }
            }
        }

        :deep(.n-tabs .n-tabs-tab-wrapper) {
            width: 215px;
            height: 72px;
            background: #ffffff;

            font-size: 18px;
            justify-content: center;
        }

        :deep(.n-tabs .n-tabs-tab .n-tabs-tab__label) {
            font-family: Microsoft YaHei UI;
            font-weight: 400;
            font-size: 18px;
        }

        :deep(.n-tabs.n-tabs--line-type .n-tabs-tab:hover, .n-tabs.n-tabs--bar-type .n-tabs-tab:hover) {
            color: #348CDE;
        }

        :deep(.n-tabs.n-tabs--line-type .n-tabs-tab.n-tabs-tab--active, .n-tabs.n-tabs--bar-type .n-tabs-tab.n-tabs-tab--active) {
            color: #348CDE;
            background: #F1F8FF;
            width: 100%;
            display: flex;
            justify-content: center;
            border-top: solid 2px #378FDF;
        }

        :deep(.n-tabs .n-tabs-tab-pad) {
            width: 0;
        }


    }

    .topding {
        position: fixed;
        top: 0;
        margin-top: 0;
        right: 312px;
        width: 430px;
    }
}

.top_bar {
    margin: 0px auto;
    width: 1300px;
    padding-left: 100px;
    padding-top: 50px;
}

@media (max-width: 1440px) {
    .top_search {
        width: 920px;
        margin-left: 373px;

    }

    .News_box {
        width: 1032px;
        margin-left: 270px;
    }
    .top_bar {
        margin: 0px;
        width: 1032px;
        margin-left: 270px;
    }
    .lef_box{
        width: 567px!important;
    }
    .topding {
        right: 138px!important;
    }
}
@media (max-width: 1366px) {
    .topding {
        right: 64px!important;
    }

}
@media (max-width: 1280px) {
    .top_search {
        width: 800px;
        margin-left: 357px;
    }

    .News_box {
        width: 900px;
        margin-left: 260px;
    }
    .rig_box{
        width: 340px!important;
    }
    .lef_box{
        width: 505px!important;
    }
    .topding {
        right: 120px!important;
    }
    :deep(.n-tabs-tab-wrapper){
        width: 170px!important;
    }
    .top_bar {
        margin: 0px;
        width: 900px;
        margin-left: 260px;
    }
}
</style>
