<template>
    <!-- 左边导航栏 -->
    <div class="bgimg">
        <div class="pc-nav">

            <div class="pc-nav-item" @click="gotoList(1)">
                <img :src="crrent == 1 ? '/img/index/home1.png' : '/img/index/home.png'" alt=""></img>
                <span :class="['span', { 'active': crrent == 1 }]">官网首页</span>
                <img v-if="crrent == 1" src="/img/index/arrow.png" alt=""
                    style="width: 9px;height: 12px;margin-left: 10px;">
            </div>
            <div class="pc-nav-item" @click="gotoList(2)">
                <img :src="crrent == 2 ? '/img/index/aboutus1.png' : '/img/index/aboutus.png'" alt=""></img>
                <span :class="['span', { 'active': crrent == 2 }]">组织机构</span>
                <img v-if="crrent == 2" src="/img/index/arrow.png" alt=""
                    style="width: 9px;height: 12px;margin-left: 10px;">
            </div>
            <div class="pc-nav-item" @click="gotoList(3)">
                <img :src="crrent == 3 ? '/img/index/news1.png' : '/img/index/news.png'" alt=""></img>
                <span :class="['span', { 'active': crrent == 3 }]">新闻动态</span>
                <img v-if="crrent == 3" src="/img/index/arrow.png" alt=""
                    style="width: 9px;height: 12px;margin-left: 10px;">
            </div>
            <div class="pc-nav-item" @click="gotoList(4)">
                <img :src="crrent == 4 ? '/img/index/ly1.png' : '/img/index/ly.png'" alt=""></img>
                <span :class="['span', { 'active': crrent == 4 }]">青春洛阳</span>
                <img v-if="crrent == 4" src="/img/index/arrow.png" alt=""
                    style="width: 9px;height: 12px;margin-left: 10px;">
            </div>
            <div class="pc-nav-item" @click="gotoList(5)">
                <img :src="crrent == 5 ? '/img/index/news1.png' : '/img/index/news.png'" alt=""></img>
                <span :class="['span', { 'active': crrent == 5 }]">服务青年</span>
                <img v-if="crrent == 5" src="/img/index/arrow.png" alt=""
                    style="width: 9px;height: 12px;margin-left: 10px;">
            </div>
            <div class="pc-nav-item" @click="gotoList(6)">
                <img :src="crrent == 6 ? '/img/index/message1.png' : '/img/index/message.png'" alt=""></img>
                <span :class="['span', { 'active': crrent == 6 }]">信息公开</span>
                <img v-if="crrent == 6" src="/img/index/arrow.png" alt=""
                    style="width: 9px;height: 12px;margin-left: 10px;">
            </div>
            <div class="pc-nav-item" @click="gotoList(7)">
                <img :src="crrent == 7 ? '/img/index/tt1.png' : '/img/index/tt.png'" alt=""></img>
                <span :class="['span', { 'active': crrent == 7 }]">团团留言板</span>
                <img v-if="crrent == 7" src="/img/index/arrow.png" alt=""
                    style="width: 9px;height: 12px;margin-left: 10px;">
            </div>
            <!-- <div class="pc-nav-item" @click="gotoList(8)">
                <img :src="crrent==8?'/img/index/gx1.png':'/img/index/gx.png'" alt=""></img>
                <span :class="['span',{'active': crrent==8}]">团关系转接</span>
                <img v-if="crrent==8" src="/img/index/arrow.png" alt="" style="width: 9px;height: 12px;margin-left: 10px;">
            </div> -->
        </div>
        <div class="line_h"></div>
        <!-- <div class="goback" @click="goBack">返回首页</div> -->
        <div style="margin-top: 50px;margin-left: 40px;">
            <div class="box_1" id='assist-open'>无障碍阅读</div>
            <!-- <div class="box_2" @click="openOldMode">{{ isOld ? '退出适老模式' : '进入适老模式' }}</div> -->
        </div>
        <div style="margin-top: 50px;margin-left: 35px;">
            <iframe allowtransparency="true" frameborder="0" width="220" height="28" scrolling="no"
                src="//tianqi.2345.com/plugin/widget/index.htm?s=3&z=1&t=0&v=0&d=1&bd=0&k=000000&f=ffffff&ltf=ffffff&htf=ffffff&q=0&e=0&a=0&c=57073&w=200&h=28&align=left"></iframe>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, defineEmits, defineProps } from 'vue'
import '@/assets/assist/assist-entry.js'
const router = useRouter();
const props = defineProps({
    crrentNum: Number,
    isOld: Boolean
});
const crrent = ref(0);
const emit = defineEmits(['crrentTop'])
const goBack = () => {
    if (router.currentRoute.value.path != '/') {
        window.open(`/`, '_blank');
    }
    emit('crrentTop', 0)
}

const openOldMode = () => {
    emit('openOldMode')
}

const gotoList = (index: number) => {
    console.log(router.currentRoute);
    if (router.currentRoute.value.path != '/') {
        window.open(`/`, '_blank');
        localStorage.setItem('index_detail', index)
    }
    crrent.value = index
    emit('crrentTop', index)

}
//更新当前索引值
onUpdated(() => {
    crrent.value = props.crrentNum;
})

onMounted(() => {
    crrent.value = props.crrentNum;
})
</script>
<style lang="scss" scoped>
@import '@/assets/index.scss';

.pc-nav {
    margin-left: 60px;
    margin-top: 80px;
    text-align: center;
}

.bgimg {
    width: 350px;
    height: 100vh;
    background-image: url('/img/index/leftHead.png');
    background-size: 100% 100%;
    position: absolute;
    z-index: 1000;

    .line_h {
        width: 220px;
        height: 1px;
        background: #FFFFFF;
        opacity: 0.2;
        margin-left: 40px;
    }

    .goback {
        width: 72px;
        height: 17px;
        font-family: Microsoft YaHei UI;
        font-weight: 400;
        font-size: 18px;
        color: #FFFFFF;
        margin-left: 114px;
        margin-top: 20px;
        cursor: pointer;
    }

    .box_1 {
        width: 200px;
        height: 58px;
        line-height: 58px;
        background: #FFA234;
        font-family: Microsoft YaHei UI;
        font-weight: 400;
        font-size: 18px;
        text-align: center;
        color: #FFFFFF;
        cursor: pointer;
    }

    .box_2 {
        width: 200px;
        height: 58px;
        line-height: 58px;
        background: #4EB64B;
        margin-top: 16px;
        font-family: Microsoft YaHei UI;
        font-weight: 400;
        font-size: 18px;
        color: #FFFFFF;
        text-align: center;
        cursor: pointer;
    }
}

.pc-nav-item {
    width: 100%;
    // height: 18px;
    font-family: Microsoft YaHei UI;
    font-weight: bold;
    font-size: 18px;
    color: #FFFFFF;
    text-shadow: 0px 1px 0px rgba(0, 0, 64, 0.4);
    display: flex;
    align-items: center;
    margin-bottom: 30px;

    &:hover {
        cursor: pointer;
        color: #FFA234;
    }

    .span {
        margin-left: 20px;

        &.active {
            color: #FFA234;
        }
    }
}

@media screen and (max-width: 1366px) {
    .pc-nav-item {
        margin-bottom: 20px;
    }

    .pc-nav {
        margin-top: 40px;
    }
}

@media screen and (max-width: 1280px) {
    .pc-nav-item {
        margin-bottom: 20px;
    }

    .pc-nav {
        margin-top: 60px;
    }
}
</style>