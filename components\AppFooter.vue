<template>
  <!-- PC端布局 -->
  <div class="group_30 flex-col pc-footer">
    <div class="text-wrapper_13 flex-row">
      <div class="fly1s" style="margin-top: 40px;width: 100%;">
        <div class="db-flex">
          <div class="b1" @click="openT()">中央人民政府</div>
          <div style="display: flex;">
            <div class="b1" style="padding: 0;">
              <select @change="getSelect" style="width: 100%;height: 41px;padding-left:15px;">
                <option value="0">省级共青团网站</option>
                <option v-for="item in s_list" :value="item.url">{{ item.title }}</option>
              </select>
            </div>
            <div>
              <img :src="`/img/index/bi.png`" style="height: 45px;width: 45px;cursor: pointer;"></img>
            </div>
          </div>
          <div style="display: flex;">
            <div class="b1" style="padding: 0;">
              <select @change="getSelect" style="width: 100%;height: 41px;padding-left:15px;">
                <option value="0">各地共青团网站</option>
                <option v-for="item in d_list" :value="item.url">{{ item.title }}</option>
              </select>
            </div>
            <div>
              <img :src="`/img/index/bi.png`" style="height: 45px;width: 45px;cursor: pointer;"></img>
            </div>
          </div>
          <div style="display: flex;">
            <div class="b1" style="padding: 0;">
              <select @change="getSelect" style="width: 100%;height: 41px;padding-left:15px;">
                <option value="0">其他</option>
                <option v-for="item in q_list" :value="item.url">{{ item.title }}</option>
              </select>
            </div>
            <div>
              <img :src="`/img/index/bi.png`" style="height: 45px;width: 45px;cursor: pointer;"></img>
            </div>
          </div>
          <!-- <div style="display: flex;">
            <div class="b1" style="padding: 0;">
              <select @change="getSelect" style="width: 100%;height: 41px;padding-left:15px;">
                <option value="0">办公入口</option>
                <option v-for="item in b_list" :value="item.url">{{ item.title }}</option>
              </select>
            </div>
            <div>
              <img :src="`/img/index/bi.png`" style="height: 45px;width: 45px;cursor: pointer;"></img>
            </div>
          </div> -->
        </div>
      </div>
      <!-- <span class="text_66">友情链接:</span>
      <NuxtLink to="https://www.gov.cn/" target="_blank">
        <span class="text_67">中央人民政府门户网站</span>
      </NuxtLink>
      <NuxtLink to="https://www.gqt.org.cn/" target="_blank">
        <span class="text_67">共青团中央</span>
      </NuxtLink>
      <NuxtLink to="https://www.henan.gov.cn/" target="_blank">
        <span class="text_67">河南省人民政府网站</span>
      </NuxtLink>
      <NuxtLink to="https://www.hnyouth.org.cn/" target="_blank">
        <span class="text_67">共青团河南省委</span>
      </NuxtLink>
      <NuxtLink to="https://www.ly.gov.cn/" target="_blank">
        <span class="text_67">洛阳市人民政府网站</span>
      </NuxtLink> -->
    </div>
    <div class="group_31 flex-col"></div>
    <div class="group_32 flex-row justify-between">
      <div class="text-wrapper_14 flex-col">
        <span class="text_74">
          ©2021&nbsp;备案号：中国共产主义青年团洛阳市委员会　版权所有
        </span>
        <span class="text_74">
          备案号：豫ICP备2021030615号-1 　Email：<EMAIL>　
        </span>
        <span class="text_74">电话：0379-63225071</span>
        <span class="text_74">
          地址：河南省洛阳市洛龙区展览路20号（市总工会大楼）3楼　邮编：471000
        </span>
      </div>
      <div class="flex-row align-center text-center">
        <img class="image_29" referrerpolicy="no-referrer" :src="`/img/index/dzjg.png`" />
        <div class="ml-[20px]">
          <img class="image_30" referrerpolicy="no-referrer" :src="`/img/wxcode.jpg`" />
          <p class="mt-[10px]">官方微信</p>
        </div>
        <div class="ml-[20px]">
          <img class="image_30" referrerpolicy="no-referrer" :src="`/img/dycode.jpg`" />
          <p class="mt-[10px]">官方抖音</p>
        </div>
        <div class="ml-[20px]">
          <img class="image_30" referrerpolicy="no-referrer" :src="`/img/wbcode.jpg`" />
          <p class="mt-[10px]">官方微博</p>
        </div>

        <div class="ml-[20px]">
          <img class="image_30" referrerpolicy="no-referrer" :src="`/img/bilicode.jpg`" />
          <p class="mt-[10px]">bilibili官方</p>
        </div>


      </div>
    </div>
  </div>

  <!-- 移动端布局 -->
  <div class="mobile-footer flex-col">
    <div class="mobile-nav flex-col">
      <span class="mobile-nav-item">企业首页</span>
      <span class="mobile-nav-item">产品&amp;服务</span>
      <span class="mobile-nav-item">灵睿&amp;我们</span>
      <span class="mobile-nav-item">客户&amp;评价</span>
      <span class="mobile-nav-item">社会&amp;责任</span>
      <span class="mobile-nav-item">联系我们</span>
      <span class="mobile-contact">联系电话：************</span>
    </div>
    <div class="mobile-info flex-col">
      <span class="mobile-company">洛阳灵睿网络技术有限公司</span>
      <span class="mobile-legal">法律顾问：河南森合律师事务机构</span>
      <span class="mobile-copyright">©2021 洛阳灵睿网络技术有限公司 All rights reserved.</span>
      <span class="mobile-icp">豫ICP备15023627号-3</span>
      <span class="mobile-address">洛阳公司地址：洛阳市 洛龙区 世贸中心D座1816室</span>
    </div>
    <div class="mobile-social flex-row">
      <img class="mobile-social-icon" referrerpolicy="no-referrer" :src="`/img/dycode.png`" />
      <img class="mobile-social-icon" referrerpolicy="no-referrer" :src="`/img/wbcode.png`" />
      <img class="mobile-social-icon" referrerpolicy="no-referrer" :src="`/img/wxcode.png`" />
      <img class="mobile-social-icon" referrerpolicy="no-referrer" :src="`/img/bilicode.png`" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useStore } from '~/store'

const store = useStore();

const getSelect = (event: Event) => {
  const target = event.target as HTMLSelectElement;
  var url = target.value;
  if (url == '0') {
    return;
  } else {
    window.open(url);
  }
}

// 使用store中的分类数据
const s_list = computed(() => store.s_list);
const d_list = computed(() => store.d_list);
const q_list = computed(() => store.q_list);
const b_list = computed(() => store.b_list);

onMounted(() => {
  // 调用store中的方法获取友情链接数据
  store.getFriendshipLinks();
})

const openT = () => {
  window.open('https://www.gov.cn/');
}
</script>

<style lang="scss" scoped>
@import '@/assets/index.scss';

/* PC端默认样式保持不变 */
.mobile-footer {
  display: none;
}

/* 移动端样式 */
@media screen and (max-width: 768px) {
  .pc-footer {
    display: none;
  }

  .mobile-footer {
    display: flex;
    padding: 20px;
    background-color: #f8f8f8;
    width: 100%;
  }

  .mobile-nav {
    gap: 15px;
    margin-bottom: 20px;
    text-align: center;
  }

  .mobile-nav-item {
    font-size: 14px;
    color: #333;
  }

  .mobile-contact {
    font-size: 14px;
    color: #666;
    margin-top: 10px;
  }

  .mobile-info {
    gap: 10px;
    text-align: center;
    margin-bottom: 20px;
  }

  .mobile-company {
    font-size: 14px;
    color: #333;
  }

  .mobile-legal,
  .mobile-copyright,
  .mobile-icp,
  .mobile-address {
    font-size: 12px;
    color: #666;
  }

  .mobile-social {
    justify-content: center;
    gap: 20px;
  }

  .mobile-social-icon {
    width: 150px;
    height: 150px;
  }
}

.b1 {
  padding-left: 15px;
  font-size: 18px;
  color: #323232;
  height: 45px;
  line-height: 45px;
  width: 220px;
  background: #FFFFFF;
  border: 1px solid #EEEEEE;
  cursor: pointer;
}

.db-flex {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 30px;
}

@media (max-width: 1440px) {
  .group_30 {
    width: 920px;
    margin-left: 380px;
  }

  .db-flex {
    gap: 10px;
  }

  .b1 {
    width: 180px;
  }

  .image_29 {
    margin-right: 0px;
  }
  .image_30{
    width: 70px;
    height: 70px;
  }
}
@media (max-width: 1366px) {
  .group_30 {
    width: 870px;
    margin-left: 357px;
  }

  .db-flex {
    gap: 10px;
  }

  .b1 {
    width: 180px;
  }

  .image_29 {
    margin-right: 0px;
  }
  .image_30{
    width: 50px;
    height: 50px;
  }
}
@media (max-width: 1280px) {
  .group_30 {
    width: 800px;
    margin-left: 357px;
  }

  .db-flex {
    gap: 10px;
  }

  .b1 {
    width: 180px;
  }

  .image_29 {
    margin-right: 0px;
  }
  .image_30{
    width: 50px;
    height: 50px;
  }
}
</style>
