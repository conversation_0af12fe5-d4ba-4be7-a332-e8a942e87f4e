<template>
    <div class="w-full h-full" style="padding: 20px;">
			<!-- 右边内容栏 -->
			<div class="rightcon" id="homeid">
				<div class="h-auto home_con">
					<div class="fly">
						<div class="re_box re_box_left">
							<div style="display: flex;justify-content: center;align-items: center;">
								<div style="color: #41B2FE;">───</div>
								<div><img class="re_top_img" :src="`/img/index/wzjx.png`"></img></div>
								<div style="color: #41B2FE;">───</div>
							</div>
							<div class="swiper">
								<swiper class="swiper_s" @swiper="onSwiperImgs" v-bind="swiperOptionsimgs">
									<swiper-slide v-for="(item, index) in xsImg" :key="index"
										@click="openUrl(item.url)">
										<!-- <div class="img_tits">{{ item.title }}</div> -->
										<img :src="item.image" style="width: 100%;height: 235px;object-fit: cover;">
									</swiper-slide>
								</swiper>
								<div class="swiper-pagination"></div>
							</div>
						</div>
					</div>
					<div class="fly1">
						<div class="re_box">
							<div class="re_box_item" v-for="(item, index) in zmList" :key="index"
								@click="openUrl(item.id)">
								<div class="re_title">
									<div class="flex align-center">
										<span class="one-line-ellipsis lx-span">{{ item.title }}</span>
									</div>
								</div>
								<div v-if="zmList.length != index + 1"
									style="width: 100%;height: 1px;background-color: #EEEEEE;margin: 15px 0px;"></div>
							</div>
							<div @click="openTab(3)"
								style="margin: 20px 10px;text-align: center;background-color: #EEEEEE;height: 50px;width: 100%;line-height: 50px;">
								查看更多 ></div>
						</div>
					</div>
				</div>

			</div>
			<div class="h-auto home_con1">
				<div class="fly1s">
					<div style="">
						<div class="tab-scroll-container" style="padding: 15px 0px 5px 0px;">
							<div class="dddccc">
								<div v-for="(item, index) in listType" :key="index"
									:class="listIndex == item.id ? 't2' : 't1'" @click="changeListIndex(item.id)">
									<div>{{ item.name }}</div>
									<div v-if="listIndex == item.id" class="t_line"></div>
								</div>
							</div>
						</div>
						<div v-for="(item, index) in jcList" :key="index" @click="openUrl(item.id)">
							<div class="re_box_item">
								<div class="re_title">
									<div class="flex align-center">
										<div class="dian"></div>
										<span class="one-line-ellipsis jc-span">{{ item.title }}</span>
									</div>
								</div>
							</div>
							<div v-if="index != 6"
								style="width: 95%;height: 1px;background-color: #EEEEEE;margin: 15px 0;"></div>
						</div>
						<div @click="openTab(3)"
							style="margin: 20px 10px;text-align: center;background-color: #EEEEEE;height: 50px;line-height: 50px;">
							查看更多 ></div>
					</div>
				</div>
			</div>
			<div class="fly1s" style="margin-top: 10px;">
				<div style="display: flex;justify-content: center;align-items: center;padding-top: 15px;">
					<div>
						<img :src="`/img/index/r.png`" alt=""></img>
					</div>
				</div>
				<div class="qn" style="">
					<div v-for="item in fwImg" @click="openWeb(item)">
						<img :src="item.image" alt=""></img>
					</div>
				</div>
			</div>
		</div>
</template>

<script lang="ts" setup>
import $api from '@/service/webRequest'
import { defineEmits } from 'vue'
import { Swiper, SwiperSlide } from 'swiper/vue'
import emitter from '@/plugins/emitter'
import 'swiper/css'
import 'swiper/css/autoplay'
import "swiper/css/pagination";
import { Autoplay, Navigation, Pagination, Scrollbar, A11y, EffectCoverflow, EffectFade, Mousewheel } from "swiper/modules";
const inputSearch = ref('');

const xsImg = ref([]);
const listType = ref([]);
const listIndex = ref(0);
const detailId = ref(null);

const emit = defineEmits(['toTab'])
const swiperOptionsimgs = {
	autoplay: {
		delay: 5000,
		disableOnInteraction: false,
	},
	direction: 'horizontal',
	slidesPerView: 1,
	speed: 500,
	// effect: 'fade',
	loop: true,
	modules: [Autoplay, Pagination],
	pagination: {
		el: '.swiper-pagination'
	},
}

const openTab = (type: number) => {
	emit('toTab', type);
}
const openWeb = (item) => {
	//out 外链   in内部
	if (item.type == 'out') {
		window.open(item.url);
	} else {
		window.open(`/phone_info/${item.url}?type=1`)
	}
	console.log(item)
}
const openUrl = (id: number) => {
	window.location.href=`/phone_info/${id}?type=1`;
}
//跳转详情
// const gotoDetail = (id: number) => {
//      detailId.value = {
//         id: id,
//         type: '1'
//     };
//     localStorage.setItem('detailId', JSON.stringify(detailId.value));
//     emit('toSwpe',6)
//     emitter.emit('detailId', detailId.value);
// }
let newsSwiper: any = null
const onSwiperImgs = (swiper: any) => {
	newsSwiper = swiper
	// swiper.on('slideChange', () => {
	// 	// 更新当前活动幻灯片索引

	// })
}
//文章精选
const zmList = ref([]);

//国务院百事通
const ptList = ref([]);
const jcList = ref([]);
const qcList = ref([]);
const mtList = ref([]);

//新闻分类
const getNewstypeList = async () => {
	let ptggid = null, jcdtid = null, qclyid = null, mtjjid = null;    // 分类id 
	const res = await $api.get('/api/home.news/cate?home=1')
	var list = res.data.data.list;
	listIndex.value = list[0].id;
	listType.value = list;
	list.map((item: any) => {
		if (item.name == '通知公告') {
			ptggid = item.id;
		}
		// if (item.name == '基层动态') {
		//     jcdtid = item.id;
		// }

		// if (item.name == '青春洛阳') {
		//     qclyid = item.id;
		// }
		// if (item.name == '媒体聚焦') {
		//     mtjjid = item.id;
		// }
	});
	//去除平台公告
	listType.value = listType.value.filter((item: any) => item.name != '平台公告');
	console.log(listType.value);
	changeListIndex(listIndex.value);
	// xsId.value = listType.value[0].id;
	//逐梦新声列表 //万众瞩目列表
	// const ress = await $api.get(`/api/home.news/index?cate_id=${xsId.value}&page=1&limit=4`)
	// xsList.value = ress.data.data.list;

	//精选文章
	const ress1 = await $api.get(`/api/home.news/index?page=1&limit=5&fine=1`)
	zmList.value = ress1.data.data.list;

	// //平台公告
	const ress2 = await $api.get(`/api/home.news/index?cate_id=${ptggid}&page=1&limit=5`)
	ptList.value = ress2.data.data.list;
	// //基层动态
	// const ress3 = await $api.get(`/api/home.news/index?cate_id=${jcdtid}&page=1&limit=5`)
	// jcList.value = ress3.data.data.list;
	// //青春洛阳
	// const ress4 = await $api.get(`/api/home.news/index?cate_id=${qclyid}&page=1&limit=5`)
	// qcList.value = ress4.data.data.list;
	// //媒体聚焦
	// const ress5 = await $api.get(`/api/home.news/index?cate_id=${mtjjid}&page=1&limit=5`)
	// mtList.value = ress5.data.data.list;
}

//逐梦新声轮播图
const getxsImg = async () => {
	const res = await $api.get('/api/index/images?page=1&limit=9&status=1')
	xsImg.value = res.data.data.list;
}
const fwImg = ref([]);
//服务青年轮播图
const getFwImg = async () => {
	const res = await $api.get('/api/index/images?page=1&limit=9&status=2')
	fwImg.value = res.data.data.list;
}
onMounted(() => {
	//window.addEventListener('scroll', hmScroll);
	getxsImg();
	getFwImg();
	getNewstypeList();
})
const changeListIndex = async (id: number) => {
	listIndex.value = id;
	const ress2 = await $api.get(`/api/home.news/index?cate_id=${id}&page=1&limit=7`)
	jcList.value = ress2.data.data.list;
}
</script>

<style lang="scss" scoped>
@import '@/assets/index.scss';


/* pc端样式 */

.swiper {

	.swiper-pagination {
		text-align: right;
		padding-right: 10px;
		bottom: 0;
		height: 38px;
		line-height: 38px;
		--swiper-pagination-color: #ffffff;
		--swiper-pagination-bullet-inactive-color: rgb(255, 255, 255, 0.5);
		--swiper-pagination-bullet-inactive-opacity: 1
			/* 两种都可以 */
	}

	.img_tits {
		font-family: Microsoft YaHei UI;
		font-weight: bold;
		font-size: 18px;
		color: #FFFEFE;
		position: absolute;
		bottom: 0;
		left: 0;
		background: rgba(0, 0, 0, 0.44);
		width: 100%;
		height: 38px;
		line-height: 38px;
		padding-left: 10px;
	}
}



.rightcon {
	width: 100%;
	height: 100%;
	background-color: #ffffff;
}

.home_con {
	.fly {
		width: 100%;

		// padding-left: 80px;
		.swiper_s {
			width: 100%;
		}
	}

	.fly1 {
		width: 100%;
		margin-top: 20px;
	}

	.re_box {
		width: 100%;
		display: flex;
		flex-wrap: wrap;
		justify-content: center;
		// gap: 10px;
		position: relative;

		.re_top_img {
			padding: 20px;
		}

		.more_j {
			font-family: Microsoft YaHei UI;
			font-weight: 400;
			font-size: 16px;
			color: #999999;
			display: flex;
			align-items: center;
			cursor: pointer;
		}

		.re_top_img_line {
			width: 100%;
			height: 2px;
			background: #EEEEEE;
			margin-left: 20px;
		}

	}

	.re_box_left {
		border-radius: 12px 0px 0px 12px;
	}
}

.re_box_item {
	width: 100%;
	box-sizing: border-box;
	padding: 0px 10px;
	margin-top: 15px;

	.re_title {
		width: 100%;
		font-family: Microsoft YaHei;
		font-weight: 400;
		font-size: 16px;
		color: #323232;
		display: flex;
		align-items: center;
		justify-content: space-between;
		cursor: pointer;
	}

	// &:hover{
	//     background-image: url('/img/index/listhmbg.png');
	//     background-size: 100% 100%;
	//     cursor: pointer;
	//     .slices_rol{
	//         width: 10px;
	//         height: 88px;
	//         background: #FAA828;
	//         position: absolute;
	//         right: 0;
	//     }
	// }
}

.home_con1 {
	width: 100%;
	box-sizing: border-box;
	background-color: #FFFEFE;
	// margin: 0 auto;
	margin-top: 10px;
	padding-bottom: 1px;
}

.fly1s {
	background-color: #ffffff;
	width: 100%;
}

.home_con2 {
	width: 100%;
	box-sizing: border-box;
	// margin: 0 auto;
	// margin-top: 10px;
	flex-wrap: wrap;



	.re_boxs {
		width: 100%;
		height: 406px;
		display: flex;
		flex-wrap: wrap;
		justify-content: flex-start;
		align-content: flex-start;
		// gap: 10px;
		background: #FFFFFF;
		position: relative;
		border-radius: 12px;

		.re_top_img {
			padding: 20px;
		}

		.more_j {
			font-family: Microsoft YaHei UI;
			font-weight: 400;
			font-size: 16px;
			color: #999999;
			display: flex;
			align-items: center;
			cursor: pointer;
		}

		.re_box_item {
			width: 100%;
			padding: 20px 20px;
			box-sizing: border-box;
			display: flex;
			justify-content: space-between;
			align-items: center;

			.re_title {
				width: 100%;
				font-family: Microsoft YaHei;
				font-weight: 400;
				font-size: 16px;
				color: #323232;
				display: flex;
				align-items: center;
				justify-content: space-between;
				cursor: pointer;


			}

			// &:hover{
			//     background-image: url('/img/index/listhmbg.png');
			//     background-size: 100% 100%;
			//     cursor: pointer;
			//     .slices_rol{
			//         width: 10px;
			//         height: 88px;
			//         background: #FAA828;
			//         position: absolute;
			//         right: 0;
			//     }
			// }
		}
	}
}

.dian {
	width: 10px;
	height: 10px;
	background: #3B90DF;
	border-radius: 50%;
	margin-right: 15px;
}

.t1 {
	font-weight: bold;
	font-family: PingFang SC;
	color: #999999;
	font-size: 14px;
	cursor: pointer;
	position: relative;
	white-space: nowrap;
	min-width: max-content;
	padding: 8px 12px;
	flex-shrink: 0;
}

.t1:hover {
	color: #2C8AE0;
}

.t2 {
	font-weight: bold;
	font-family: PingFang SC;
	color: #2C8AE0;
	font-size: 14px;
	cursor: pointer;
	position: relative;
	white-space: nowrap;
	min-width: max-content;
	padding: 8px 12px;
	flex-shrink: 0;
}

.t_line {
	width: 40%;
	height: 3px;
	background-color: #3E92DE;
	margin: 0 auto;
	position: absolute;
	left: 0;
	right: 0;
	bottom: -5px;
}

.qn {
	padding: 10px;
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 10px;
	margin-top: 20px;
}

.qn div {
	cursor: pointer;
}

.tz-span {}

.jc-span {
	width: 100%;
}

.lx-span {}

/* 滚动容器样式 */
.tab-scroll-container {
	width: 100%;
	overflow-x: auto;
	overflow-y: hidden;
	/* 隐藏滚动条但保持滚动功能 */
	scrollbar-width: none;
	/* Firefox */
	-ms-overflow-style: none;
	/* IE and Edge */
}

.tab-scroll-container::-webkit-scrollbar {
	display: none;
	/* Chrome, Safari and Opera */
}

.dddccc {
	display: flex;
	align-items: center;
	flex-wrap: nowrap;
	min-width: max-content;
}
</style>
