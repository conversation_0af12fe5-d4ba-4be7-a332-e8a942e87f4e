<template>
    <!-- PC端布局 -->
    <div class="w-full h-full" style="padding: 20px;">
        <div style="background-color: #ffffff;padding: 10px;">
            <div style="display: flex;justify-content: center;align-items: center;text-align: center;">
                <div><img class="re_top_img" :src="`/img/fuwu2.png`" style="width: 100%;margin: 0 auto;"></img></div>
            </div>
            <div class="qn" style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px; margin-top: 20px;">
                <div style="cursor: pointer;" v-for="item in fwImg" @click="openWeb(item)">
                    <img :src="item.image" alt=""></img>
                </div>
            </div>
        </div>
        <div style="background-color: #ffffff;margin-top: 10px;padding:20px 10px 20px 10px;">
            <div style="padding-bottom: 20px;">
                <img src="/img/tuan.png" style="width: 200px;margin: 0 auto;">
            </div>
            <div v-for="(item, index) in MesList" :key="index" @click="openUrl(item.id)">
                <div class="re_box_item">
                    <div class="re_title">
                        <div class="flex align-center">
                            <div class="dian"></div>
                            <span class="one-line-ellipsis jc-span">{{ item.title }}</span>
                        </div>
                    </div>
                </div>
                <div v-if="index != 6" style="width: 95%;height: 1px;background-color: #EEEEEE;margin: 15px 0;"></div>
            </div>
            <!-- <div style="margin: 20px 10px 10px 10px;text-align: center;background-color: #EEEEEE;height: 50px;line-height: 50px;">
                查看更多 ></div> -->
        </div>
    </div>
</template>

<script lang="ts" setup>
import { defineEmits } from 'vue'
import { NPagination } from 'naive-ui'
import $api from '@/service/webRequest'
import emitter from '@/plugins/emitter'
const inputSearch = ref('');
const MesList = ref([]);
const newsListtyp = ref([]);
const page = ref(1);
const total = ref(0);
const cateid = ref(1);
const detailId = ref(null);
const showCode = ref(0);
const showCodeDo = (id: number) => {
    showCode.value = id;
}
// 信息公开
onMounted(() => {
    getFwImg();
    getMesList();
    getYwList();
    //getNewstypeList();
})
const fwImg = ref([]);
//服务青年轮播图
const getFwImg = async () => {
    const res = await $api.get('/api/index/images?page=1&limit=9&status=2')
    fwImg.value = res.data.data.list;
}
const openWeb = (item) => {
    //out 外链   in内部
    if (item.type == 'out') {
        window.open(item.url);
    } else {
        window.open(`/info/${item.url}?type=1`)
    }
    console.log(item)
}

const toMore = () => {
    window.open(`/messageBoard`)
}
//跳转详情
const openUrl = (id: number) => {
    window.location.href=`/phone_info/${id}?type=3`;
}
//跳转详情
// const gotoDetail = (id: number) => {
//     detailId.value = {
//         id: id,
//         type: '1'
//     };
//     localStorage.setItem('detailId', JSON.stringify(detailId.value));
//     emit('toSwpe',6)
//     emitter.emit('detailId', detailId.value);
// }
//搜索
const toSearch = () => {
    // emit('toSwpe',9);
    // emitter.emit('inputSea', {
    //     keywords:inputSearch.value,
    //     type: 1,
    // });
    window.open(`/search_info?type=1&keywords=${inputSearch.value}`)
}
const getMesList = async () => {
    const res = await $api.post('/api/home.encyclopedia/index',
        {
            cate_id: '102',
            limit: 7,
            page: 1,
        }
    )
    MesList.value = res.data.data.list;
}
const YwList = ref([]);
const getYwList = async () => {
    const res = await $api.post('/api/home.encyclopedia/index',
        {
            cate_id: '103',
            limit: 7,
            page: 1,
        }
    )
    YwList.value = res.data.data.list;
}
const getNewstypeList = async () => {
    const res = await $api.get('/api/home.news/cate?isnews=1')
    newsListtyp.value = res.data.data.list;
}
</script>

<style lang="scss" scoped>
@import '@/assets/index.scss';

.dian {
    width: 10px;
    height: 10px;
    background: #3B90DF;
    border-radius: 50%;
    margin-right: 15px;
}

.jc-span {
    width: 100%;
}
</style>
