<template>
    <div class="w-full h-full" style="padding: 20px;">
        <div style="display: flex;justify-content: center;align-items: center;text-align: center;">
            <div><img class="re_top_img" :src="`/img/index/zzjg.png`" style="width: 80%;margin: 0 auto;"></img></div>
        </div>
        <div style="background-color: #ffffff;padding: 10px;margin-top: 20px;">
            <div style="display: flex;justify-content: space-between;align-items: center;">
                <div v-for="(item, index) in newsListtyp" @click="getMesList(item.id)" style="position: relative;">
                    <div :class="['lef_boxitem2', { 'active': cateid == item.id }]">{{ item.tabtitle }}</div>
                    <div v-if="cateid == item.id"
                        style="height: 3px;background-color: #2C8AE0;width: 30px;margin: 0 auto;">
                    </div>
                </div>
            </div>
            <div>
                <div
                    style="color: #999999;font-weight: 400;font-size: 16px;line-height: 25px;white-space: pre-line;margin-top: 30px;">
                    <div v-html="info.content"></div>
                </div>
            </div>
        </div>
        <div style="height: 1px;width: 100%;background-color: #F8F8F8;"></div>
    </div>
</template>

<script lang="ts" setup>
import { defineEmits } from 'vue'
import { NPagination } from 'naive-ui'
import $api from '@/service/webRequest'
const newsListtyp = ref([]);
const cateid = ref(0);

// 信息公开
onMounted(() => {
    //getMesList();
    getNewstypeList(1);
})


const info = ref({});
const getMesList = async (id: number) => {
    cateid.value = id;
    info.value = newsListtyp.value.find(item => item.id == id);
}
const getNewstypeList = async () => {
    const res = await $api.get(`/api/index/about_us`)
    newsListtyp.value = res.data.data.list;
    cateid.value = res.data.data.list[0].id;
    info.value = res.data.data.list[0];
}
</script>

<style lang="scss" scoped>
@import '@/assets/index.scss';

.lef_boxitem2 {
    color: #323232;
    font-size: 16px;
}

.active {
    color: #2C8AE0;
    font-weight: 600;
}
</style>