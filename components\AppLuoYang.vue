<template>
    <!-- PC端布局 -->
    <div class="flex justify-between w-full h-full">
        <!-- 左边导航栏 -->


        <!-- 右边内容栏 -->
        <div class="rightcon" id="luoyangid" @scroll="hmScroll">
            <div class="flex top_search ">
                <div style="width: 350px;">
                    <img @click="toMore(0)" :src="`/img/index/dlogo.png`"
                        style="cursor: pointer;;width: 100%;height: 100%;object-fit: cover;">
                </div>
                <div class="inputform">
                    <input v-model="inputSearch" class="contactInput w-full md:w-[337px]" type="text"
                        placeholder="请输入搜索关键字" />
                    <div class="search_rinput" @click="toSearch">
                        <img src="/img/index/hmsearch.png" alt="">
                    </div>
                </div>
            </div>

            <div class="News_box">
                <div class="lef_box">
                    <div>
                        <img src="/img/luoyang.png" style="width: 100%;">
                    </div>
                    <div
                        style="background: #ffffff;width: 100%;display: flex;align-items: center;padding: 15px;margin-top: 20px;">
                        <div>
                            <img :src="`/img/key.png`" style="width: 22px;height: 22px;">
                        </div>
                        <div style="font-size: 18px;color: #323232;margin-left: 10px;">当前位置：首页 > 青春洛阳 > <span
                                style="color: #3B90DF;">{{newsListtyp.find(item => item.id == cateid)?.name
                                }}</span></div>
                    </div>
                    <div style="display: flex;margin-top: 25px;gap: 15px;">
                        <div>
                            <div v-for="(item, index) in newsListtyp" :key="index"
                                :class="cateid == item.id ? 'le1' : 'le2'" @click="getMesList(item.id)">{{ item.name }}
                            </div>
                        </div>
                        <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px;">
                            <div v-for="item in MesList" class="ly_list" @mousemove="showCodeDo(item)"
                                @mouseleave="showCodeDo(0)" @click="showCodeDo(item)">
                                <div>
                                    <img class="ly_list_img" :src="item.image">
                                    <div class="ly_list_text">{{ item.title }}</div>
                                </div>
                                <div v-if="showCode == item"
                                    style="display: flex;justify-content: center;align-items: center;position: absolute;bottom: 0;left: 0;right: 0;width: 100%;height:100%;background: rgba(0,0,0,0.5);">
                                    <img class="ly_list_img_act" :src="item.qrimage">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
            <div class="flex flex-row justify-center pages_tsw" style="margin-top: 20px;">
                <n-pagination v-model:page="page" :page-sizes="[9]" :item-count="total" size="medium"
                    @update:page="getPageList" show-quick-jumper>
                    <template #goto>
                        到第
                    </template>
                    <template #prefix="{ itemCount }">
                        共 {{ itemCount }} 条
                    </template>
                </n-pagination>
            </div>
            <AppFooter></AppFooter>
            <!-- 右分享 -->
            <!-- <div style="position: absolute;top: 300px;right:36px;" class="flex flex-col fiximg">
            <img :src="`/img/index/douyin.png`" alt="">
            <img :src="`/img/index/wb.png`" alt="">
            <img :src="`/img/index/wx.png`" alt="">
            <img :src="`/img/index/blbl.png`" alt="">
        </div> -->
            <!-- 底部鼠标 -->
            <div class="mouse_img">
                <img :src="`/img/index/hmouse.png`" alt="">
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { defineEmits } from 'vue'
import { NPagination } from 'naive-ui'
import $api from '@/service/webRequest'
import emitter from '@/plugins/emitter'
const inputSearch = ref('');
const MesList = ref([]);
const newsListtyp = ref([]);
const page = ref(1);
const total = ref(0);
const cateid = ref(1);
const detailId = ref(null);
const showCode = ref(0);
const showCodeDo = (id: number) => {
    showCode.value = id;
}
const getPageList = async () => {
    page.value = page.value;
    getMesList(cateid.value);
}
// 信息公开
onMounted(() => {
    //getMesList();
    getNewstypeList();
})
const hmScroll = () => {
    let home = document.getElementById('luoyangid');
    if (home.scrollTop + home.clientHeight >= home.scrollHeight) {
        emit('toSwpe', 5);
    }
    if (home.scrollTop == 0) {
        emit('toSwpe', 3);
    }
}
const emit = defineEmits(['toSwpe'])
const toMore = (index: number) => {
    emit('toSwpe', index);
}

//搜索
const toSearch = () => {
    // emit('toSwpe',9);
    // emitter.emit('inputSea', {
    //     keywords:inputSearch.value,
    //     type: 1,
    // });
    window.open(`/search_info?type=1&keywords=${inputSearch.value}`)
}
const getMesList = async (id: number) => {
    cateid.value = id;
    //cateid.value = id ? id : cateid.value;
    // page.value = pagecru ? pagecru : page.value;
    const res = await $api.post('/api/home.media_matrix/index',
        {
            cate_id: cateid.value,
            page: page.value,
            limit: 9
        }
    )
    total.value = res.data.data.count;
    MesList.value = res.data.data.list;
}
const getNewstypeList = async () => {
    const res = await $api.get('/api/home.media_matrix/cate')
    newsListtyp.value = res.data.data.list;
    getMesList(res.data.data.list[0].id);
}
</script>

<style lang="scss" scoped>
@import '@/assets/index.scss';


/* pc端样式 */

.rightcon {
    width: 100%;
    height: 100%;
    background-image: url('/img/index/rightcons.png');
    background-size: 100% 100%;
    overflow-y: auto;

    .fiximg {
        img {
            margin-bottom: 20px;
        }
    }
}

.top_search {
    margin: 0 auto;
    margin-top: 50px;
    height: 70px;
    justify-content: space-between;
    align-items: center;
    margin-left: 415px;
    width: 1200px;
}

.inputform {
    position: relative;
    width: 340px;
}

.contactInput {
    width: 337px;
    height: 50px;
    background: #FFFFFF;
    border-radius: 8px;
    border: 1px solid #DEE4E8;
    padding-left: 15px;
}

.contactInput::placeholder {
    color: #999999;
}

.contactInput:focus {
    outline: none;
}

.search_rinput {
    width: 68px;
    height: 50px;
    background: #338CDE;
    border-radius: 0px 8px 8px 0px;
    position: absolute;
    top: 0;
    right: 0;
    display: flex;
    justify-content: center;
    align-items: center;
}

.News_box {
    width: 1300px;
    margin: 0 auto;
}

.lef_box {
    height: 100%;
    margin-top: 40px;
    margin-left: 100px;
}

.le1 {
    color: #338CDE;
    font-family: PingFang SC;
    font-size: 22px;
    font-weight: 800;
    width: 261px;
    height: 65px;
    line-height: 65px;
    text-align: center;
    margin-bottom: 20px;
    cursor: pointer;
    border: 1px solid #338CDE;
    background-image: url('/img/ly_listbg.png');
    background-size: 100% 100%;
}

.le2 {
    color: #323232;
    font-family: PingFang SC;
    font-size: 22px;
    font-weight: 400;
    width: 261px;
    height: 65px;
    background-color: #ffffff;
    line-height: 65px;
    text-align: center;
    margin-bottom: 20px;
    cursor: pointer;
    border: 1px solid transparent;
}

.le2:hover {
    border: 1px solid #338CDE;
}

.ly_list {
    width: 298px;
    height: 298px;
    background-color: #ffffff;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    position: relative;
}

.ly_list_img {
    width: 100px;
    height: 100px;
    margin: 0 auto;
}

.ly_list_text {
    font-size: 22px;
    color: #323232;
    font-weight: 400;
    text-align: center;
    margin-top: 15px;
}

.ly_list_img_act {
    width: 150px;
    height: 150px;
    margin: 0 auto;
}

.pages_tsw {
    padding: 30px 0px 0px 270px;

    :deep(.n-pagination .n-pagination-item:not(.n-pagination-item--disabled).n-pagination-item--active) {
        color: #ffffff;
        background-color: #388FDF;
        border: 1px solid #388FDF;
    }

    :deep(.n-pagination .n-pagination-item:not(.n-pagination-item--disabled):hover.n-pagination-item--button) {
        color: #999999;
    }

    :deep(.n-pagination .n-pagination-item:not(.n-pagination-item--disabled):hover) {
        color: #388FDF;
        // border: 1px solid #388FDF;
    }

    :deep(.n-pagination .n-pagination-item--active:hover) {
        color: #ffffff !important;
        // border: 1px solid #388FDF;
    }

    :deep(.n-pagination .n-pagination-item) {
        border: none;
        width: 42px;
        height: 30px;
        font-size: 18px;
        background: #FFFFFF;
    }

    :deep(.n-pagination-quick-jumper) {
        &:after {
            content: '页'
        }
    }
}

@media (max-width: 1440px) {
    .top_search {
        width: 920px;
        margin-left: 373px;

    }

    .News_box {
        width: 1032px;
        margin-left: 270px;
    }

    .rig_box {
        width: 700px;
    }

    .ly_list {
        width: 209px;
        height: 209px;
    }

    .ly_list_img {
        width: 70px;
        height: 70px;
        margin: 0 auto;
    }

    .ly_list_img_act {
        width: 120px;
        height: 120px;
        margin: 0 auto;
    }

    .ly_list_text {
        font-size: 20px;
    }
}
@media (max-width: 1366px) {
    .top_search {
        width: 870px;
        margin-left: 357px;

    }

    .News_box {
        width: 970px;
        margin-left: 250px;
    }

    .ly_list {
        width: 188px;
        height: 188px;
    }

    .ly_list_img {
        width: 60px;
        height: 60px;
        margin: 0 auto;
    }

    .ly_list_img_act {
        width: 100px;
        height: 100px;
        margin: 0 auto;
    }

    .ly_list_text {
        font-size: 16px;
    }
}
@media (max-width: 1280px) {
    .top_search {
        width: 800px;
        margin-left: 357px;

    }

    .News_box {
        width: 900px;
        margin-left: 250px;
    }

    .ly_list {
        width: 165px;
        height: 165px;
    }

    .ly_list_img {
        width: 50px;
        height: 50px;
        margin: 0 auto;
    }

    .ly_list_img_act {
        width: 80px;
        height: 80px;
        margin: 0 auto;
    }

    .ly_list_text {
        font-size: 16px;
    }
}
</style>
