<template>
    <div class="w-full h-full" style="padding: 20px;">
        <div style="background-color: #fff;border-radius: 10px;padding: 20px;position: relative;">
            <img :src="`/img/tt.png`" style="position: absolute;top: -7px;left: 0;right: 0;margin: auto;width: 150px;;">
            <div style="display: flex;align-items: center;margin-top: 30px;">
                <div>姓名</div>
                <div style="width: 90%;">
                    <input type="text" placeholder="请输入您的姓名" v-model="formData.name" />
                </div>
            </div>
            <div style="display: flex;align-items: center;margin-top: 20px;">
                <div>电话</div>
                <div style="width: 90%;">
                    <input type="text" placeholder="请输入您的电话" v-model="formData.mobile" />
                </div>
            </div>
            <div style="display: flex;align-items: center;margin-top: 20px;">
                <div>问题</div>
                <div style="width: 90%;">
                    <input type="text" placeholder="请输入您的问题" v-model="formData.question" />
                </div>
            </div>
            <div style="display: flex;margin-top: 20px;">
                <div>描述</div>
                <div style="width: 90%;">
                    <textarea placeholder="请输入您的描述" :maxlength="300" v-model="formData.message"
                        style="margin-left: 10px;width: 100%;height: 100px;border: 1px solid #BBBBBB;border-radius: 6px;padding: 7px;"></textarea>
                </div>
            </div>
            <div>
                <button @click="submitForm" class="contactBut">提交</button>
            </div>
        </div>
        <div style="margin-top: 20px;">
            <div style="display: flex;justify-content: center;align-items: center;gap: 10px;">
                <div style="background-color: #358DDE;width: 35px;height: 2px;"></div>
                <div style="font-size: 23px;font-weight: 600;color: #358DDE;">精选留言</div>
                <div style="background-color: #358DDE;width: 35px;height: 2px;"></div>
            </div>
        </div>
        <div class="re_list_top" style="gap: 20px;">
            <div class="re_list" v-for="(item, index) in liuyanList"
                style="background-color: #fff;border-radius: 8px;padding: 20px;margin-top: 10px;">
                <div>
                    <div class="re_list_tit flex" style="width: 100%;">
                        <span style="width: 17%;color: #FFA234;font-weight: 600;">问题：</span>
                        <span style="color: #323232;width:85%;font-weight: 600;">{{ item.question }}</span>
                    </div>
                    <div class="re_list_tit flex" style="margin-top: 15px;width: 100%;">
                        <span style="width: 17%;color: #FFA234;font-weight: 600;">描述：</span>
                        <span :title="item.message" class="three-line-ellipsis"
                            style="font-weight: 400;font-size: 14px;color: #999999;width:85% ;">{{ item.message
                            }}</span>
                    </div>
                    <div class="re_list_tit flex" style="margin-top: 15px;">
                        <span style="color: #368FDF;width: 17%;font-weight: 600;">回复：</span>
                        <span :title="item.answer"
                            style="font-weight: 400;font-size: 14px;color: #999999;width:85% ;">{{ item.answer
                            }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

</template>

<script lang="ts" setup>
import { defineEmits } from 'vue'
import $api from '@/service/webRequest'
const inputSearch = ref('');
// 表单数据
const formData = reactive({
    name: '',
    mobile: '',
    question: '',
    message: ''
})
const total = ref(0);
const liuyanList = ref([]);

const getLiuyanList = async () => {
    const res = await $api.post('/api/home.leave_word/index',
        {
            limit: 4,
            page: 1,
            status: 2,
            show: 1

        }
    )
    total.value = res.data.data.count;
    liuyanList.value = res.data.data.list;
}
onMounted(() => {
    getLiuyanList();
})
const toSearch = () => {
    // emit('toSwpe',9);
    // emitter.emit('inputSea', {
    //     keywords:inputSearch.value,
    //     type: 1,
    // });
    window.open(`/search_info?type=1&keywords=${inputSearch.value}`)
}
const emit = defineEmits(['toSwpe'])
const toMore = (index: number) => {
    emit('toSwpe', index);
}
const hmScroll = () => {
    let home = document.getElementById('contid');
    console.log(home.scrollTop);
    if (home.scrollTop == 0) {
        emit('toSwpe', 6);
    }
}
// 表单方法
const submitForm = () => {
    console.log(formData);

    // 这里可以添加表单验证逻辑
    if (!formData.name) {
        alert('请输入您的姓名')
        return
    }

    if (!formData.mobile) {
        alert('请输入您的手机号码')
        return
    }

    if (!formData.question) {
        alert('请输入您的问题')
        return
    }

    if (!formData.message) {
        alert('请输入您的留言内容')
        return
    }
    $api.post("/api/home.leave_word/add", formData)
        .then((res: any) => {
            console.log(res)
            if (res.status == 200) {
                alert('留言成功')
            } else {
                alert('失败')
            }
            formData.name = '';
            formData.mobile = '';
            formData.question = '';
            formData.message = '';
        })
        .catch((err) => {
            console.dir(err)
        })
    // 重置表单
    // formData.name = ''
    // formData.mobile = ''
    // formData.content = ''
}
</script>

<style lang="scss" scoped>
@import '@/assets/index.scss';

input {
    border: 1px solid #BBBBBB;
    border-radius: 6px;
    padding: 7px;
    margin-left: 10px;
    width: 100%;
}
.contactBut {
    width: 70px;
    border-radius: 4px 4px 4px 4px;
    // position: absolute;
    // bottom: 20px;
    // right: 30px;
    height: 30px;
    margin-top: 10px;
    margin-left: 40px;
    background: linear-gradient(0deg, #338CDE 0%, #469CE2 100%);
    border: none;
    font-weight: bold;
    font-size: 14px;
    color: #FFFFFF;
}
</style>
