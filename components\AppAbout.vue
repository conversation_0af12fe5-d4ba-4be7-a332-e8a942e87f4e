<template>
    <!-- PC端布局 -->
    <div class="flex justify-between w-full h-full">
        <!-- 左边导航栏 -->


        <!-- 右边内容栏 -->
        <div class="rightcon" id="aboutid" @scroll="hmScroll">
            <div class="flex top_search ">
                <div style="width: 350x;">
                    <img @click="toMore(0)" :src="`/img/index/dlogo.png`"  style="cursor: pointer;;width: 100%;height: 100%;object-fit: cover;">
                </div>
                <div class="inputform">
                    <input v-model="inputSearch" class="contactInput w-full md:w-[337px]" type="text"
                        placeholder="请输入搜索关键字" />
                    <div class="search_rinput" @click="toSearch">
                        <img src="/img/index/hmsearch.png" alt="">
                    </div>
                </div>
            </div>

            <div class="News_box">
                <div class="lef_box">
                    <!-- @click="getMesList(null)" 新闻动态总 -->
                    <div class="lef_boxitem1">
                        <div class="lef_boxitem1_shu"></div>
                        <div class="lef_boxitem1_title">组织机构</div>
                    </div>
                    <div :class="['lef_boxitem2', { 'active': cateid == item.id }]" v-for="(item, index) in newsListtyp"
                        :key="index" @click="getMesList(item.id, 1)">
                        <div class="lef_boxitem2_title">{{ item.tabtitle }}</div>
                    </div>
                </div>
                <div class="rig_box">
                    <div class="re_box">
                        <div style="background: #ffffff;width: 100%;display: flex;align-items: center;padding: 18px;">
                            <div>
                                <img :src="`/img/key.png`" style="width: 22px;height: 22px;">
                            </div>
                            <div style="font-size: 18px;color: #323232;margin-left: 10px;">当前位置：首页 > 关于我们 > <span
                                    style="color: #3B90DF;">{{newsListtyp.find(item => item.id == cateid)?.tabtitle
                                    }}</span></div>
                        </div>
                        <div style="background-color: #ffffff;width: 100%;padding: 18px;">
                            <div
                                style="font-weight: bold;font-size: 32px;color: #393939;text-align: center;padding: 25px 0px 25px 0;">{{ info.title }}</div>
                            <div style="width: 100%;height: 3px;border-top: 1px dashed #BBBBBB;"></div>
                            <div
                                style="color: #999999;font-weight: 400;font-size: 16px;line-height: 25px;white-space: pre-line;margin-top: 30px;">
                                <div v-html="info.content"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


            <!-- 右分享 -->
            <!-- <div style="position: absolute;top: 300px;right:36px;" class="flex flex-col fiximg">
              <img :src="`/img/index/douyin.png`" alt="">
              <img :src="`/img/index/wb.png`" alt="">
              <img :src="`/img/index/wx.png`" alt="">
              <img :src="`/img/index/blbl.png`" alt=""> 
          </div> -->
            <AppFooter></AppFooter>
            <!-- 底部鼠标 -->
            <div class="mouse_img">
                <img :src="`/img/index/hmouse.png`" alt="">
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { defineEmits } from 'vue'
import { NPagination } from 'naive-ui'
import $api from '@/service/webRequest'
import emitter from '@/plugins/emitter'
const inputSearch = ref('');
const MesList = ref([]);
const newsListtyp = ref([]);
const page = ref(1);
const total = ref(0);
const cateid = ref(0);
const detailId = ref(null);

const ldbz = ref('')

// 信息公开
onMounted(() => {
    //getMesList();
    getNewstypeList(1);
})
const hmScroll = () => {
    let home = document.getElementById('aboutid');
    if (home.scrollTop + home.clientHeight >= home.scrollHeight) {
        emit('toSwpe', 3);
    }
    if (home.scrollTop == 0) {
        emit('toSwpe', 1);
    }
}
const emit = defineEmits(['toSwpe'])
const toMore = (index:number) => {
    emit('toSwpe', index);
}

//搜索
const toSearch = () => {
    
    window.open(`/search_info?type=1&keywords=${inputSearch.value}`)
}
const info=ref({});
const getMesList=async(id:number)=>{
    cateid.value=id;
    info.value=newsListtyp.value.find(item=>item.id==id);
}
const getNewstypeList = async () => {
    const res = await $api.get(`/api/index/about_us`)
    newsListtyp.value = res.data.data.list;
    cateid.value=res.data.data.list[0].id;
    info.value=res.data.data.list[0];
}
</script>

<style lang="scss" scoped>
@import '@/assets/index.scss';


/* pc端样式 */

.rightcon {
    width: 100%;
    height: 100%;
    background-image: url('/img/index/rightcons.png');
    background-size: 100% 100%;
    overflow-y: auto;

    .fiximg {
        img {
            margin-bottom: 20px;
        }
    }
}

.top_search {
    margin: 0 auto;
    margin-top: 50px;
    height: 70px;
    justify-content: space-between;
    align-items: center;
    margin-left: 415px;
    width: 1200px;
}

.inputform {
    position: relative;
    width: 340px;
}

.contactInput {
    width: 337px;
    height: 50px;
    background: #FFFFFF;
    border-radius: 8px;
    border: 1px solid #DEE4E8;
    padding-left: 15px;
}

.contactInput::placeholder {
    color: #999999;
}

.contactInput:focus {
    outline: none;
}

.search_rinput {
    width: 68px;
    height: 50px;
    background: #338CDE;
    border-radius: 0px 8px 8px 0px;
    position: absolute;
    top: 0;
    right: 0;
    display: flex;
    justify-content: center;
    align-items: center;
}

.News_box {
    display: flex;
    justify-content: space-between;
    width: 1300px;
    margin: 0 auto;
    gap: 20px;

    .lef_box {
        width: 260px;
        height: 100%;
        margin-top: 40px;
        margin-left: 100px;

        .lef_boxitem1 {
            width: 260px;
            height: 64px;
            background: linear-gradient(0deg, #338CDE 0%, #3D92E0 100%);
            display: flex;
            align-items: center;
            justify-content: center;

            // cursor: pointer;
            .lef_boxitem1_title {
                width: 88px;
                // height: 22px;
                font-family: Microsoft YaHei UI;
                font-weight: bold;
                font-size: 22px;
                color: #FFFEFE;
            }

            .lef_boxitem1_shu {
                width: 4px;
                height: 25px;
                background: #FFFFFF;
                margin-right: 20px;
            }
        }

        .lef_boxitem2 {
            width: 260px;
            height: 64px;
            background: #FFFFFF;
            color: #323232;
            display: flex;
            align-items: center;
            margin-top: 10px;
            cursor: pointer;

            .lef_boxitem2_title {
                width: 100%;
                // height: 19px;
                font-family: Microsoft YaHei;
                font-weight: 400;
                font-size: 20px;
                text-align: center;
            }

            &:hover {
                background: linear-gradient(0deg, rgba(51, 140, 222, 0.12) 0%, rgba(61, 146, 224, 0.12) 100%);
                color: #3A91DF;

            }

            &.active {
                background: #388FDF;
                color: #ffffff;
            }
        }
    }

    .rig_box {
        width: 100%;
        .re_box {
            width: 100%;
            height: 100%;
            margin: 0 auto;
            margin-top: 40px;
            // padding-left: 60px;
            display: flex;
            flex-wrap: wrap;
            justify-content: flex-start;
            align-content: flex-start;
            gap: 10px;

            .re_box_item {
                width: 100%;
                height: 90px;
                background: #FFFFFF;
                border: 1px solid #EEF7FF;
                padding: 20px;
                box-sizing: border-box;
                display: flex;
                justify-content: space-between;
                align-items: center;

                .re_title {
                    // width: 640px;
                    font-family: Microsoft YaHei;
                    font-weight: 400;
                    font-size: 18px;
                    color: #323232;
                    display: flex;
                    align-items: center;

                    .dian {
                        width: 10px;
                        height: 10px;
                        background: #3B90DF;
                        border-radius: 50%;
                        margin-right: 15px;
                    }
                }

                .re_date {
                    font-family: Microsoft YaHei;
                    font-weight: 400;
                    font-size: 14px;
                    color: #368BDB;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;

                    .shu {
                        background: #EEEEEE;
                        width: 1px;
                        height: 50px;
                        margin-right: 20px;
                    }

                    .right_tex {
                        display: flex;
                        flex-direction: column;
                        width: 80px;
                        text-align: center;

                        .span1 {
                            width: 100%;
                            font-family: Microsoft YaHei;
                            font-weight: 400;
                            font-size: 22px;
                            color: #378EDF;
                        }

                        .span2 {
                            width: 100%;
                            font-family: Microsoft YaHei;
                            font-weight: 400;
                            font-size: 16px;
                            color: #378EDF;
                        }
                    }
                }

                &:hover {
                    background-image: url('/img/message/listbg.png');
                    background-size: 100% 100%;
                    cursor: pointer;

                    .re_date {
                        .shu {
                            background: #FFFFFF;
                        }
                    }
                }
            }
        }

        .pages_tsw {
            :deep(.n-pagination .n-pagination-item:not(.n-pagination-item--disabled).n-pagination-item--active) {
                color: #ffffff;
                background-color: #388FDF;
                border: 1px solid #388FDF;
            }

            :deep(.n-pagination .n-pagination-item:not(.n-pagination-item--disabled):hover.n-pagination-item--button) {
                color: #999999;
            }

            :deep(.n-pagination .n-pagination-item:not(.n-pagination-item--disabled):hover) {
                color: #388FDF;
                // border: 1px solid #388FDF;
            }

            :deep(.n-pagination .n-pagination-item--active:hover) {
                color: #ffffff !important;
                // border: 1px solid #388FDF;
            }

            :deep(.n-pagination .n-pagination-item) {
                border: none;
                width: 42px;
                height: 30px;
                font-size: 18px;
                background: #FFFFFF;
            }

            :deep(.n-pagination-quick-jumper) {
                &:after {
                    content: '页'
                }
            }
        }
    }
}

@media (max-width: 1440px) {
    .top_search {
        width: 920px;
        margin-left: 373px;
        
    }
    .News_box{
        width: 1032px;
        margin-left: 270px;
    }
    .rig_box{
        width: 700px;
    }
}
@media (max-width: 1366px) {
    .top_search {
        width: 860px;
        margin-left: 357px;
        
    }
    .News_box{
        width: 970px;
        margin-left: 250px;
    }
    .rig_box{
        width: 700px;
    }
}
@media (max-width: 1280px) {
    .top_search {
        width: 800px;
        margin-left: 357px;
        
    }
    .News_box{
        width: 900px;
        margin-left: 250px;
    }
    .rig_box{
        width: 700px;
    }
}
</style>