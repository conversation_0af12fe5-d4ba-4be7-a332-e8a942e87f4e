import { defineNuxtPlugin } from '#app'

export default defineNuxtPlugin((nuxtApp) => {
  const config = useRuntimeConfig()
  
  // 定义全局变量
  const cdnUrl = config.public.cdnDomain
  console.log(cdnUrl);
  
  // 在客户端注入 CSS 变量
  if (process.client) {
    document.documentElement.style.setProperty('--cdn-domain', cdnUrl)
  }
  
  // 只使用一种注入方式
  return {
    provide: {
      cdnUrl
    }
  }
})

// 为了 TypeScript 支持
declare module '#app' {
  interface NuxtApp {
    $cdnUrl: string
  }
}

declare module '@vue/runtime-core' {
  interface ComponentCustomProperties {
    $cdnUrl: string
  }
} 