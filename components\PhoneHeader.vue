<template>
    <div>
        <div class="group_666" style="position: relative;height: 219px;">
            <img :src="`/img/index/phone_top.png`" style="width: 100%;height: 100%;object-fit: cover;">
            <img :src="`/img/index/dlogo1.png`"
                style="width: 100px;height: 100px;position: absolute;left: 0;right: 0;top: 40px;margin: 0 auto;">
            <img :src="`/img/xuan.png`" @click="show = true"
                style="width: 25px;height: 25px;position: absolute;right: 20px;top: 30px;">
        </div>
        <div v-if="show"
            style="z-index: 999;;background-color: rgba(0, 0, 0, 0.5);height: 100%;width: 100%;top: 0;position: fixed;">
        </div>
        <div class="bgimg" v-if="show">
            <img :src="`/img/guan.png`" @click="show = false"
                style="width: 25px;height: 25px;position: absolute;right: 20px;top: 30px;">
            <div class="pc-nav">

                <div class="pc-nav-item" @click="gotoList(1)">
                    <img :src="crrentNum == 1 ? '/img/index/home1.png' : '/img/index/home.png'" alt=""></img>
                    <span :class="['span', { 'active': crrentNum == 1 }]">官网首页</span>
                    <img v-if="crrentNum == 1" src="/img/index/arrow.png" alt=""
                        style="width: 9px;height: 12px;margin-left: 10px;">
                </div>
                <div class="pc-nav-item" @click="gotoList(2)">
                    <img :src="crrentNum == 2 ? '/img/index/aboutus1.png' : '/img/index/aboutus.png'" alt=""></img>
                    <span :class="['span', { 'active': crrentNum == 2 }]">组织机构</span>
                    <img v-if="crrentNum == 2" src="/img/index/arrow.png" alt=""
                        style="width: 9px;height: 12px;margin-left: 10px;">
                </div>
                <div class="pc-nav-item" @click="gotoList(3)">
                    <img :src="crrentNum == 3 ? '/img/index/news1.png' : '/img/index/news.png'" alt=""></img>
                    <span :class="['span', { 'active': crrentNum == 3 }]">新闻动态</span>
                    <img v-if="crrentNum == 3" src="/img/index/arrow.png" alt=""
                        style="width: 9px;height: 12px;margin-left: 10px;">
                </div>
                <div class="pc-nav-item" @click="gotoList(4)">
                    <img :src="crrentNum == 4 ? '/img/index/ly1.png' : '/img/index/ly.png'" alt=""></img>
                    <span :class="['span', { 'active': crrentNum == 4 }]">青春洛阳</span>
                    <img v-if="crrentNum == 4" src="/img/index/arrow.png" alt=""
                        style="width: 9px;height: 12px;margin-left: 10px;">
                </div>
                <div class="pc-nav-item" @click="gotoList(5)">
                    <img :src="crrentNum == 5 ? '/img/index/news1.png' : '/img/index/news.png'" alt=""></img>
                    <span :class="['span', { 'active': crrentNum == 5 }]">服务青年</span>
                    <img v-if="crrentNum == 5" src="/img/index/arrow.png" alt=""
                        style="width: 9px;height: 12px;margin-left: 10px;">
                </div>
                <div class="pc-nav-item" @click="gotoList(6)">
                    <img :src="crrentNum == 6 ? '/img/index/message1.png' : '/img/index/message.png'" alt=""></img>
                    <span :class="['span', { 'active': crrentNum == 6 }]">信息公开</span>
                    <img v-if="crrentNum == 6" src="/img/index/arrow.png" alt=""
                        style="width: 9px;height: 12px;margin-left: 10px;">
                </div>
                <div class="pc-nav-item" @click="gotoList(7)">
                    <img :src="crrentNum == 7 ? '/img/index/tt1.png' : '/img/index/tt.png'" alt=""></img>
                    <span :class="['span', { 'active': crrentNum == 7 }]">团团留言板</span>
                    <img v-if="crrentNum == 7" src="/img/index/arrow.png" alt=""
                        style="width: 9px;height: 12px;margin-left: 10px;">
                </div>
                <!-- <div class="pc-nav-item" @click="gotoList(8)">
                <img :src="crrent==8?'/img/index/gx1.png':'/img/index/gx.png'" alt=""></img>
                <span :class="['span',{'active': crrent==8}]">团关系转接</span>
                <img v-if="crrent==8" src="/img/index/arrow.png" alt="" style="width: 9px;height: 12px;margin-left: 10px;">
            </div> -->
            </div>
            <!-- <div class="goback" @click="goBack">返回首页</div> -->
            <!-- <div style="margin-top: 80px;margin-left: 80px;">
                <div class="box_1" id='assist-open'>无障碍阅读</div>
                <div class="box_2" @click="openOldMode">{{ isOld ? '退出适老模式' : '进入适老模式' }}</div>
            </div> -->
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, defineEmits, defineProps } from 'vue'
import '@/assets/assist/assist-entry.js'
const router = useRouter();
const props = defineProps({
    crrentNum: Number,
    isOld: Boolean
});
const crrent = ref(0);
const show = ref(false);
const emit = defineEmits(['crrentTop'])
const goBack = () => {
    if (router.currentRoute.value.path != '/') {
        window.open(`/`, '_blank');
    }
    emit('crrentTop', 0)
}

const openOldMode = () => {
    emit('openOldMode')
}

const gotoList = (index: number) => {
    // console.log(router.currentRoute);
    // if (router.currentRoute.value.path != '/') {
    //     window.open(`/`, '_blank');
    //     localStorage.setItem('index_detail', index)
    // }
    // crrent.value = index
    show.value = false;
    emit('phoneCrrent', index)

}
//更新当前索引值
onUpdated(() => {
    crrent.value = props.crrentNum;
})

onMounted(() => {
    crrent.value = props.crrentNum;
})
</script>
<style lang="scss" scoped>
@import '@/assets/index.scss';

.bgimg {
    width: 270px;
    height: 100vh;
    background-image: url('/img/right.png');
    background-size: 100% 100%;
    position: fixed;
    z-index: 1000;
    top: 0;
    right: 0;

    .line_h {
        width: 220px;
        height: 1px;
        background: #FFFFFF;
        opacity: 0.2;
        margin-left: 40px;
    }

    .goback {
        width: 72px;
        height: 17px;
        font-family: Microsoft YaHei UI;
        font-weight: 400;
        font-size: 18px;
        color: #FFFFFF;
        margin-left: 114px;
        margin-top: 20px;
        cursor: pointer;
    }

    .box_1 {
        width: 150px;
        height: 45px;
        line-height: 45px;
        background: #FFA234;
        font-family: Microsoft YaHei UI;
        font-weight: 400;
        font-size: 18px;
        text-align: center;
        color: #FFFFFF;
        cursor: pointer;
    }

    .box_2 {
        width: 200px;
        height: 58px;
        line-height: 58px;
        background: #4EB64B;
        margin-top: 16px;
        font-family: Microsoft YaHei UI;
        font-weight: 400;
        font-size: 18px;
        color: #FFFFFF;
        text-align: center;
        cursor: pointer;
    }
}

.pc-nav {
    margin-left: 83px;
    padding-top: 80px;
}

.pc-nav-item {
    width: 100%;
    // height: 18px;
    font-family: Microsoft YaHei UI;
    font-weight: bold;
    font-size: 18px;
    color: #FFFFFF;
    text-shadow: 0px 1px 0px rgba(0, 0, 64, 0.4);
    display: flex;
    align-items: center;
    margin-bottom: 50px;

    &:hover {
        cursor: pointer;
        color: #FFA234;
    }

    img {
        width: 40px;
        height: 40px;
    }

    .span {
        margin-left: 20px;
        font-size: 15px;

        &.active {
            color: #FFA234;
        }
    }
}
</style>