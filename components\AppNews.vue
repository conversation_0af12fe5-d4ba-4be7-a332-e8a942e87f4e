<template>
    <!-- PC端布局 -->
    <div class="flex justify-between w-full h-full">
        <!-- 左边导航栏 -->


        <!-- 右边内容栏 -->
        <div class="rightcon" id="newsid" @scroll="hmScroll">
            <div class="flex top_search ">
                <div style="width: 350px;">
                    <img @click="toMore(0)" :src="`/img/index/dlogo.png`"  style="cursor: pointer;;width: 100%;height: 100%;object-fit: cover;">
                </div>
                <div class="inputform">
                    <input v-model="inputSearch" class="contactInput w-full md:w-[337px]" type="text"
                        placeholder="请输入搜索关键字" />
                    <div class="search_rinput" @click="toSearch">
                        <img src="/img/index/hmsearch.png" alt="">
                    </div>
                </div>
            </div>

            <div class="News_box">
                <div class="lef_box">
                    <!-- @click="getMesList(null)" 新闻动态总 -->
                    <div class="lef_boxitem1">
                        <div class="lef_boxitem1_shu"></div>
                        <div class="lef_boxitem1_title">新闻动态</div>
                    </div>
                    <div :class="['lef_boxitem2', { 'active': cateid == item.id }]" v-for="(item, index) in newsListtyp"
                        :key="index" @click="getMesList(item.id, 1)">
                        <div class="lef_boxitem2_title">{{ item.name }}</div>
                    </div>
                </div>
                <div class="rig_box">
                    <div class="re_box">
                        <div class="re_box_item" v-for="(item, index) in MesList" :key="index"
                            @click="openUrl(item.id)">
                            <div class="re_title">
                                <div class="dian"></div>
                                <span class="one-line-ellipsis d_span">{{ item.title }}</span>
                            </div>
                            <div class="re_date">
                                <div class="shu"></div>
                                <div class="right_tex">
                                    <span class="span1">{{ item.release_time_text.slice(8, 10) }}</span>
                                    <span class="span2">{{ item.release_time_text.slice(0, 7) }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 分页 -->

                </div>

            </div>
            <div class="flex flex-row justify-center pages_tsw" style="margin-top: 20px;">
                <n-pagination v-model:page="page" :page-sizes="[7]" :item-count="total" size="medium"
                    @update:page="getPageList" show-quick-jumper>
                    <template #goto>
                        到第
                    </template>
                    <template #prefix="{ itemCount }">
                        共 {{ itemCount }} 条
                    </template>
                </n-pagination>
            </div>

            <AppFooter></AppFooter>
            <!-- 右分享 -->
            <!-- <div style="position: absolute;top: 300px;right:36px;" class="flex flex-col fiximg">
            <img :src="`/img/index/douyin.png`" alt="">
            <img :src="`/img/index/wb.png`" alt="">
            <img :src="`/img/index/wx.png`" alt="">
            <img :src="`/img/index/blbl.png`" alt="">
        </div> -->
            <!-- 底部鼠标 -->
            <div class="mouse_img">
                <img :src="`/img/index/hmouse.png`" alt="">
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { defineEmits } from 'vue'
import { NPagination } from 'naive-ui'
import $api from '@/service/webRequest'
import emitter from '@/plugins/emitter'
const inputSearch = ref('');
const MesList = ref([]);
const newsListtyp = ref([]);
const page = ref(1);
const total = ref(0);
const cateid = ref(null);
const detailId = ref(null);

// 信息公开
onMounted(() => {
    getMesList();
    getNewstypeList();
})
const emit = defineEmits(['toSwpe'])
const hmScroll = () => {
    let home = document.getElementById('newsid');
    if (home.scrollTop + home.clientHeight >= home.scrollHeight) {
        emit('toSwpe', 4);
    }
    if (home.scrollTop == 0) {
        emit('toSwpe', 2);
    }
}
const toMore = (index:number) => {
    emit('toSwpe', index);
}
//跳转详情
const openUrl = (id: number) => {
    window.open(`/info/${id}?type=1`)
}
//跳转详情
// const gotoDetail = (id: number) => {
//     detailId.value = {
//         id: id,
//         type: '1'
//     };
//     localStorage.setItem('detailId', JSON.stringify(detailId.value));
//     emit('toSwpe',6)
//     emitter.emit('detailId', detailId.value);
// }
//搜索
const toSearch = () => {
    // emit('toSwpe',9);
    // emitter.emit('inputSea', {
    //     keywords:inputSearch.value,
    //     type: 1,
    // });
    window.open(`/search_info?type=1&keywords=${inputSearch.value}`)
}
const getPageList = async () => {
    page.value = page.value;
    getMesList();
}
const getMesList = async (id: number, pagecru: number) => {
    cateid.value = id ? id : cateid.value;
    page.value = pagecru ? pagecru : page.value;
    const res = await $api.post('/api/home.news/index',
        {
            cate_id: cateid.value,
            limit: 9,
            page: page.value,
            isnews: 1
        }
    )
    total.value = res.data.data.count;
    MesList.value = res.data.data.list;
}
const getNewstypeList = async () => {
    const res = await $api.get('/api/home.news/cate?isnews=1')
    newsListtyp.value = res.data.data.list;
}
</script>

<style lang="scss" scoped>
@import '@/assets/index.scss';


/* pc端样式 */

.rightcon {
    width: 100%;
    height: 100%;
    background-image: url('/img/index/rightcons.png');
    background-size: 100% 100%;
    overflow-y: auto;

    .fiximg {
        img {
            margin-bottom: 20px;
        }
    }
}

.top_search {
    margin: 0 auto;
    margin-top: 50px;
    height: 70px;
    justify-content: space-between;
    align-items: center;
    margin-left: 415px;
    width: 1200px;
}

.inputform {
    position: relative;
    width: 340px;
}

.contactInput {
    width: 337px;
    height: 50px;
    background: #FFFFFF;
    border-radius: 8px;
    border: 1px solid #DEE4E8;
    padding-left: 15px;
}

.contactInput::placeholder {
    color: #999999;
}

.contactInput:focus {
    outline: none;
}

.search_rinput {
    width: 68px;
    height: 50px;
    background: #338CDE;
    border-radius: 0px 8px 8px 0px;
    position: absolute;
    top: 0;
    right: 0;
    display: flex;
    justify-content: center;
    align-items: center;
}

.News_box {
    display: flex;
    justify-content: space-between;
    width: 1300px;
    margin: 0 auto;
    gap: 20px;
    .lef_box {
        width: 260px;
        height: 100%;
        margin-top: 40px;
        margin-left: 100px;

        .lef_boxitem1 {
            width: 260px;
            height: 64px;
            background: linear-gradient(0deg, #338CDE 0%, #3D92E0 100%);
            display: flex;
            align-items: center;
            justify-content: center;

            // cursor: pointer;
            .lef_boxitem1_title {
                width: 88px;
                // height: 22px;
                font-family: Microsoft YaHei UI;
                font-weight: bold;
                font-size: 22px;
                color: #FFFEFE;
            }

            .lef_boxitem1_shu {
                width: 4px;
                height: 25px;
                background: #FFFFFF;
                margin-right: 20px;
            }
        }

        .lef_boxitem2 {
            width: 260px;
            height: 64px;
            background: #FFFFFF;
            color: #323232;
            display: flex;
            align-items: center;
            margin-top: 10px;
            cursor: pointer;

            .lef_boxitem2_title {
                width: 100%;
                // height: 19px;
                font-family: Microsoft YaHei;
                font-weight: 400;
                font-size: 20px;
                text-align: center;
            }

            &:hover {
                background: linear-gradient(0deg, rgba(51, 140, 222, 0.12) 0%, rgba(61, 146, 224, 0.12) 100%);
                color: #3A91DF;

            }

            &.active {
                background: #388FDF;
                color: #ffffff;
            }
        }
    }

    .rig_box {
        width: 921px;

        .re_box {
            width: 100%;
            margin: 0 auto;
            margin-top: 40px;
            // padding-left: 60px;
            display: flex;
            flex-wrap: wrap;
            justify-content: flex-start;
            align-content: flex-start;
            gap: 10px;

            .re_box_item {
                width: 100%;
                height: 90px;
                background: #FFFFFF;
                border: 1px solid #EEF7FF;
                padding: 20px;
                box-sizing: border-box;
                display: flex;
                justify-content: space-between;
                align-items: center;

                .re_title {
                    // width: 640px;
                    font-family: Microsoft YaHei;
                    font-weight: 400;
                    font-size: 18px;
                    color: #323232;
                    display: flex;
                    align-items: center;

                    .dian {
                        width: 10px;
                        height: 10px;
                        background: #3B90DF;
                        border-radius: 50%;
                        margin-right: 15px;
                    }
                }

                .re_date {
                    font-family: Microsoft YaHei;
                    font-weight: 400;
                    font-size: 14px;
                    color: #368BDB;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;

                    .shu {
                        background: #EEEEEE;
                        width: 1px;
                        height: 50px;
                        margin-right: 20px;
                    }

                    .right_tex {
                        display: flex;
                        flex-direction: column;
                        width: 80px;
                        text-align: center;

                        .span1 {
                            width: 100%;
                            font-family: Microsoft YaHei;
                            font-weight: 600;
                            font-size: 22px;
                            color: #378EDF;
                        }

                        .span2 {
                            width: 100%;
                            font-family: Microsoft YaHei;
                            font-weight: 400;
                            font-size: 16px;
                            color: #378EDF;
                        }
                    }
                }

                &:hover {
                    background-image: url('/img/listbg.png');
                    background-size: 100% 100%;
                    cursor: pointer;

                    .re_date {
                        .shu {
                            background: #FFFFFF;
                        }
                    }
                }
            }
        }

    }
}

.pages_tsw {
    padding:30px 0px 0px 270px;
    :deep(.n-pagination .n-pagination-item:not(.n-pagination-item--disabled).n-pagination-item--active) {
        color: #ffffff;
        background-color: #388FDF;
        border: 1px solid #388FDF;
    }

    :deep(.n-pagination .n-pagination-item:not(.n-pagination-item--disabled):hover.n-pagination-item--button) {
        color: #999999;
    }

    :deep(.n-pagination .n-pagination-item:not(.n-pagination-item--disabled):hover) {
        color: #388FDF;
        // border: 1px solid #388FDF;
    }

    :deep(.n-pagination .n-pagination-item--active:hover) {
        color: #ffffff !important;
        // border: 1px solid #388FDF;
    }

    :deep(.n-pagination .n-pagination-item) {
        border: none;
        width: 42px;
        height: 30px;
        font-size: 18px;
        background: #FFFFFF;
    }

    :deep(.n-pagination-quick-jumper) {
        &:after {
            content: '页'
        }
    }
}

@media (max-width: 1440px) {
    .top_search {
        width: 920px;
        margin-left: 373px;

    }

    .News_box {
        width: 1032px;
        margin-left: 270px;
    }

    .rig_box {
        width: 650px !important;
    }
    .d_span{
        width: 468px;
    }
}
@media (max-width: 1366px) {
    .top_search {
        width: 860px;
        margin-left: 357px;

    }

    .News_box {
        width: 960px;
        margin-left: 260px;
      
    }

    .rig_box {
        width: 650px !important;
    }
    .d_span{
        width: 328px;
    }
}
@media (max-width: 1280px) {
    .top_search {
        width: 800px;
        margin-left: 357px;

    }

    .News_box {
        width: 900px;
        margin-left: 260px;
      
    }

    .rig_box {
        width: 650px !important;
    }
    .d_span{
        width: 328px;
    }
}
</style>
