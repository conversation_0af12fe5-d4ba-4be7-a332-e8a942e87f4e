<template>
    <!-- PC端布局 -->
    <div>
        <div class="flex-row">
            <div class="fly1s" style="width: 100%;padding: 0px 20px;">
                <div style="display: flex;justify-content: center;align-items: center;gap: 10px;">
                    <div class="b1" @click="openT()">中央人民政府</div>
                    <div class="b1">
                        <select @change="getSelect" style="width: 100%;height: 41px;">
                            <option value="0">省级共青团网站</option>
                            <option v-for="item in s_list" :value="item.url">{{ item.title }}</option>
                        </select>
                        <img :src="`/img/index/bi.png`"
                            style="position: absolute;top: 7px;right: 5px;height: 30px;width: 30px;cursor: pointer;"></img>
                    </div>
                </div>
                <div style="display: flex;justify-content: center;align-items: center;gap: 10px;margin-top: 10px;">
                    <div class="b1">
                        <select @change="getSelect" style="width: 100%;height: 41px;">
                            <option value="0">各地共青团网站</option>
                            <option v-for="item in d_list" :value="item.url">{{ item.title }}</option>
                        </select>
                        <img :src="`/img/index/bi.png`"
                            style="position: absolute;top: 7px;right: 5px;height: 30px;width: 30px;cursor: pointer;"></img>
                    </div>
                    <div class="b1">
                        <select @change="getSelect" style="width: 100%;height: 41px;">
                            <option value="0">其他</option>
                            <option v-for="item in q_list" :value="item.url">{{ item.title }}</option>
                        </select>
                        <img :src="`/img/index/bi.png`"
                            style="position: absolute;top: 7px;right: 5px;height: 30px;width: 30px;cursor: pointer;"></img>
                    </div>
                </div>
            </div>
        </div>
        <div style="height: 1px;background-color:#EEEEEE;width: 100%;margin-top: 20px;"></div>
        <div class="buttom_bg" style="padding:30px;">
            <div>
                <div>
                    ©2021&nbsp;备案号：中国共产主义青年团洛阳市委员会　版权所有
                </div>
                <div style="display: flex;justify-content: space-between;align-items: center;margin: 15px 0px;">
                    <div>
                        <div>
                            备案号：豫ICP备2021030615号-1
                        </div>
                        <div>
                            Email：<EMAIL>　
                        </div>
                        <div class="">电话：0379-63225071</div>
                    </div>
                    <div>
                        <img :src="`/img/index/dzjg.png`" style="width: 50px;" />
                    </div>
                </div>
                <div class="">
                    地址：河南省洛阳市洛龙区展览路20号（市总工会大楼）3楼　邮编：471000
                </div>
            </div>
            <div
                style="display: flex;justify-content: space-between;align-items: center;gap: 15px;text-align: center;padding-top: 20px;">

                <div>
                    <img class="" referrerpolicy="no-referrer" :src="`/img/wxcode.jpg`" />
                    <p class="mt-[10px]">官方微信</p>
                </div>
                <div>
                    <img class="" referrerpolicy="no-referrer" :src="`/img/dycode.jpg`" />
                    <p class="mt-[10px]">官方抖音</p>
                </div>
                <div>
                    <img class="" referrerpolicy="no-referrer" :src="`/img/wbcode.jpg`" />
                    <p class="mt-[10px]">官方微博</p>
                </div>

                <div>
                    <img class="" referrerpolicy="no-referrer" :src="`/img/bilicode.jpg`" />
                    <p class="mt-[10px]">bilibili官方</p>
                </div>

            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { useStore } from '~/store'

const store = useStore();

const getSelect = (event: Event) => {
    const target = event.target as HTMLSelectElement;
    var url = target.value;
    if (url == '0') {
        return;
    } else {
        window.open(url);
    }
}

// 使用store中的分类数据
const s_list = computed(() => store.s_list);
const d_list = computed(() => store.d_list);
const q_list = computed(() => store.q_list);
const b_list = computed(() => store.b_list);

onMounted(() => {
    // 调用store中的方法获取友情链接数据
    store.getFriendshipLinks();
})

const openT = () => {
    window.open('https://www.gov.cn/');
}
</script>

<style lang="scss" scoped>
@import '@/assets/index.scss';

.buttom_bg {
    background-image: url('/img/index/rightcons.png');
    background-size: 100% 100%;
}

.b1 {
    width: 100%;
    padding-left: 15px;
    font-size: 14px;
    color: #323232;
    height: 45px;
    line-height: 45px;
    background: #FFFFFF;
    border: 1px solid #EEEEEE;
    cursor: pointer;
    position: relative;
}

select {
    background: #FFFFFF;
}
</style>