<template>
	<div style="width: 100%;background-color: #F0F9FF;">
		<PhoneHeader :crrentNum="phone_index" @phoneCrrent="toswiper"></PhoneHeader>
		<PhoneHome v-if="phone_index == 1" @toTab="toswiper"></PhoneHome>
		<PhoneAbout v-if="phone_index == 2"></PhoneAbout>
		<PhoneNews v-if="phone_index == 3"></PhoneNews>
		<PhoneLuoYang v-if="phone_index == 4"></PhoneLuoYang>
		<PhoneRegiment v-if="phone_index == 5"></PhoneRegiment>
		<PhoneMessage v-if="phone_index == 6"></PhoneMessage>
		<PhoneContactUs v-if="phone_index == 7"></PhoneContactUs>
		<PhoneFooter></PhoneFooter>
	</div>
</template>

<script lang="ts" setup>
import $api from '@/service/webRequest'


const phone_index = ref(1);

const toswiper = (index: number) => {
	console.log(index);
	phone_index.value = index;
}
const xsImg = ref([]);
const listType = ref([]);
const listIndex = ref(0);


//文章精选
const zmList = ref([]);

//国务院百事通
const ptList = ref([]);
const jcList = ref([]);

//新闻分类
const getNewstypeList = async () => {
	let ptggid = null, jcdtid = null, qclyid = null, mtjjid = null;    // 分类id 
	const res = await $api.get('/api/home.news/cate?home=1')
	var list = res.data.data.list;
	listIndex.value = list[0].id;
	listType.value = list;
	list.map((item: any) => {
		if (item.name == '通知公告') {
			ptggid = item.id;
		}
	});
	//去除平台公告
	listType.value = listType.value.filter((item: any) => item.name != '平台公告');
	console.log(listType.value);
	changeListIndex(listIndex.value);
	//精选文章
	const ress1 = await $api.get(`/api/home.news/index?page=1&limit=5&fine=1`)
	zmList.value = ress1.data.data.list;
	// //平台公告
	const ress2 = await $api.get(`/api/home.news/index?cate_id=${ptggid}&page=1&limit=5`)
	ptList.value = ress2.data.data.list;
}

//逐梦新声轮播图
const getxsImg = async () => {
	const res = await $api.get('/api/index/images?page=1&limit=9&status=1')
	xsImg.value = res.data.data.list;
}
const fwImg = ref([]);
//服务青年轮播图
const getFwImg = async () => {
	const res = await $api.get('/api/index/images?page=1&limit=9&status=2')
	fwImg.value = res.data.data.list;
}
onMounted(() => {
	//window.addEventListener('scroll', hmScroll);
	getxsImg();
	getFwImg();
	getNewstypeList();
})
const changeListIndex = async (id: number) => {
	listIndex.value = id;
	const ress2 = await $api.get(`/api/home.news/index?cate_id=${id}&page=1&limit=7`)
	jcList.value = ress2.data.data.list;
}
</script>

<style lang="scss" scoped>
@import '@/assets/index.scss';
</style>
