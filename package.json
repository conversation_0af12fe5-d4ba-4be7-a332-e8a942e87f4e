{"name": "nuxt-app", "private": true, "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "build:static": "node scripts/build-static.js"}, "devDependencies": {"@nuxt/devtools": "latest", "@nuxtjs/i18n": "8.0.0-beta.10", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "autoprefixer": "^10.4.16", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.0", "eslint-plugin-vue": "^9.19.2", "naive-ui": "^2.41.0", "nuxt": "^3.8.2", "prettier": "^3.1.1", "sass": "^1.69.5", "tailwindcss": "^3.4.0", "typescript": "^5.3.3", "vue": "^3.3.12", "vue-router": "^4.2.5"}, "dependencies": {"@pinia/nuxt": "^0.5.1", "ant-design-vue": "^4.0.8", "axios": "^1.6.2", "callapp-lib": "^3.5.3", "js-cookie": "^3.0.5", "mitt": "^3.0.1", "nuxt-icons": "^3.2.1", "pinia": "^2.1.7", "scrollreveal": "^4.0.9", "swiper": "^11.2.5", "vue-i18n": "^9.8.0", "vue-swiper-animate": "^1.0.0"}, "browserslist": ["cover 99.5%"]}