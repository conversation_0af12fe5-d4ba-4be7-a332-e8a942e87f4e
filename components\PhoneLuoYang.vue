<template>
    <div class="w-full h-full" style="padding: 20px;">
        <div style="display: flex;justify-content: center;align-items: center;text-align: center;">
            <div><img class="re_top_img" :src="`/img/fuwu1.png`" style="margin: 0 auto;"></img></div>
        </div>
        <div style="display: grid;grid-template-columns: repeat(3, 1fr);gap: 10px;margin-top: 20px;">
            <div v-for="(item, index) in newsListtyp" :key="index" :class="cateid == item.id ? 'le1' : 'le2'"
                @click="getMesList(item.id)">{{ item.name }}
            </div>
        </div>
        <div style="margin-top: 20px;">
            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px;">
                <div v-for="item in MesList" class="ly_list" @click="showCodeDo(item)">
                    <div>
                        <img class="ly_list_img" :src="item.image">
                        <div class="ly_list_text">{{ item.title }}</div>
                    </div>
                    <div v-if="showCode == item"
                        style="display: flex;justify-content: center;align-items: center;position: absolute;bottom: 0;left: 0;right: 0;width: 100%;height:100%;background: rgba(0,0,0,0.5);">
                        <img class="ly_list_img_act" :src="item.qrimage">
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { defineEmits } from 'vue'
import { NPagination } from 'naive-ui'
import $api from '@/service/webRequest'
import emitter from '@/plugins/emitter'
const inputSearch = ref('');
const MesList = ref([]);
const newsListtyp = ref([]);
const page = ref(1);
const total = ref(0);
const cateid = ref(1);
const detailId = ref(null);
const showCode = ref(0);
const showCodeDo = (id: number) => {
    showCode.value = id;
}
const getPageList = async () => {
    page.value = page.value;
    getMesList(cateid.value);
}
// 信息公开
onMounted(() => {
    //getMesList();
    getNewstypeList();
})

const getMesList = async (id: number) => {
    cateid.value = id;
    //cateid.value = id ? id : cateid.value;
    // page.value = pagecru ? pagecru : page.value;
    const res = await $api.post('/api/home.media_matrix/index',
        {
            cate_id: cateid.value,
            page: page.value,
            limit: 9
        }
    )
    total.value = res.data.data.count;
    MesList.value = res.data.data.list;
}
const getNewstypeList = async () => {
    const res = await $api.get('/api/home.media_matrix/cate')
    newsListtyp.value = res.data.data.list;
    getMesList(res.data.data.list[0].id);
}
</script>

<style lang="scss" scoped>
@import '@/assets/index.scss';

.le2 {
    background: #ffffff;
    height: 35px;
    line-height: 35px;
    text-align: center;
}

.le1 {
    border: 1px solid #338CDE;
    background-image: url('/img/ly_listbg.png');
    background-size: 100% 100%;
    height: 35px;
    line-height: 35px;
    text-align: center;
}

.ly_list {
    width: 168px;
    height: 168px;
    background-color: #ffffff;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    position: relative;
}

.ly_list_img {
    width:60px;
    height: 60px;
    margin: 0 auto;
}

.ly_list_text {
    font-size: 15px;
    color: #323232;
    font-weight: 400;
    text-align: center;
    margin-top: 15px;
}
</style>
