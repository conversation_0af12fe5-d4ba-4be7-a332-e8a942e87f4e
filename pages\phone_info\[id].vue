<template>
	<div style="width: 100%;">
		<div
			style="display: flex;justify-content: space-between;align-items: center;height:60px;background-color: #ffffff;">
			<div style="width: 33.3%;padding-left: 15px;" @click="backTopDark()">
				<img :src="`/img/back.png`" style="width: 30px;">
			</div>
			<div style="width: 33.3%;text-align: center;font-size: 18px;">新闻详情</div>
			<div style="width: 33.3%;"></div>
		</div>
		<div style="background-color: #eeeeee;width:100%;height: 0.5px;"></div>
		<div style="background-color: #F8F8F8;">
			<div class="content_class" style="">
				<div style="font-size: 18px;font-weight: 700;color: #323232;">{{ info.title }}</div>
				<div
					style="display: flex;justify-content: space-between;align-items: center;font-size: 14px;font-weight: 400;margin-top: 15px;color: #999999;">
					<div>发布人：{{ info.author }}</div>
					<div style="margin-top: 5px;">{{ info.release_time_text }} </div>
				</div>
				<div style="width: 100%;margin:20px 0px 0px 0px;height: 1px;background-color: #EEEEEE;"></div>
				<div class="info_content" v-html="info.content"></div>
				<div class="re_box_item" v-if="info != null && info.type == 2">
					<embed type="application/pdf" :src="info.file" width="100%" height="580px" alt="">
				</div>
				<div style="width: 100%;margin: 20px auto;height: 1px;background-color: #EEEEEE;"></div>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { nextTick, ref } from 'vue'
const route = useRoute()
import $api from '@/service/webRequest'
import { Item } from 'ant-design-vue/es/menu'
const ids = ref(route.params.id)
const types = ref(route.query.type)
const openNews = (type) => {
	if (type == 1 && info.value.prev != null) {
		window.open('/phone_info/' + info.value.prev.id, '_self');
	}
	if (type == 2 && info.value.next != null) {
		window.open('/phone_info/' + info.value.next.id, '_self');
	}
}
// 新闻数据
onMounted(() => {
	getNewsdetail();
})
const info = ref({});
const getNewsdetail = async () => {
	// 新闻详情
	if (types.value == 1) {
		const res = await $api.post('/api/home.news/detail',
			{
				id: ids.value
			}
		)
		info.value = res.data.data;
	} else if (types.value == 2) {
		//信息公开文章详情
		const res1 = await $api.post('/api/home.information/detail',
			{
				id: ids.value
			}
		)
		info.value = res1.data.data;
	} else {
		// 团务百科文章详情
		const res2 = await $api.post('/api/home.encyclopedia/detail',
			{
				id: ids.value
			}
		)
		info.value = res2.data.data;
	}

}
const delWeb = () => {
	window.close();
}
const backTopDark = () => {
	window.history.back();
}
</script>

<style lang="scss">
@import "@/assets/animate/animate.min.css";
@import "@/assets/index.scss";

.info_content img {
	width: 100% !important;
	margin: 0 auto;
	margin-bottom: 10px;
}

._1NCGf {
	width: 100% !important;
}

._2kCxD {
	margin-top: 0px !important;
}

.info_content {
	line-height: 28px !important;
	text-align: justify !important;
}

.content_class {
	width: 100%;
	min-height: 500px;
	margin: 0 auto;
	background-color: #ffffff;
	padding: 20px;
}

.re_box_item {
	width: 100%;
	background: #ffffff;
	border: 1px solid #eef7ff;
	box-sizing: border-box;
	display: flex;
	flex-direction: column;
	min-height: 750px;
}
</style>
