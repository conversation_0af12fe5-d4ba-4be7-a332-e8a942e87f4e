<template>
    <!-- PC端布局 -->
    <div class="flex justify-between w-full h-full">
        <!-- 左边导航栏 -->
        <!-- 右边内容栏 -->
        <div class="rightcon" id="regid" @scroll="hmScroll">
            <div class="flex top_search ">
                <div style="width: 350px;">
                    <img @click="toHome(0)" :src="`/img/index/dlogo.png`"
                        style="cursor: pointer;;width: 100%;height: 100%;object-fit: cover;">
                </div>
                <div class="inputform">
                    <input v-model="inputSearch" class="contactInput w-full md:w-[337px]" type="text"
                        placeholder="请输入搜索关键字" />
                    <div class="search_rinput" @click="toSearch">
                        <img src="/img/index/hmsearch.png" alt="">
                    </div>
                </div>
            </div>

            <div class="News_box">
                <div class="lef_box" style="margin-top: 50px;">
                    <div>
                        <img src="/img/fuwu.png" style="width: 100%;">
                    </div>
                    <div style="margin-top: 25px;">
                        <div class="qn"
                            style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px; margin-top: 20px;">
                            <div style="cursor: pointer;" v-for="item in fwImg" @click="openWeb(item)">
                                <img :src="item.image" alt=""></img>
                            </div>
                        </div>
                    </div>
                    <div style="margin-top: 50px;">
                        <img src="/img/tuan.png" style="width: 400px;margin: 0 auto;">
                    </div>
                    <div class="home_con1" style="margin-top: 50px;">
                        <div class="fly1s" style="display: flex;gap: 20px;">
                            <div style="width: 65%;background-color: #ffffff;">
                                <!-- <div class="no-wrap"
                                    style="padding: 15px;display: flex;justify-content: space-between;align-items: center;">
                                    <div style="display: flex;align-items: center;gap: 10px;">
                                        <div>
                                            <img src="/img/f1.png" alt="" style="width: 35px;"></img>
                                        </div>
                                        <div
                                            style="padding-left: 10px;font-family: PingFang SC;font-weight: bold;font-size: 22px;color: #323232;">
                                            热门百科
                                        </div>
                                    </div>
                                    <div class="pr-[5px]" @click="toMore()"
                                        style="display: flex;align-items: center;cursor: pointer;">
                                        <span style="color: #999999;">更多</span>
                                        <img src="/img/index/more_j.png" alt=""
                                            style="width: 8px;height: 13px;margin-left: 5px;"></img>
                                    </div>
                                </div> -->
                                <!-- <div style="width: 100%;height: 1px;background-color: #EEEEEE;"></div> -->
                                <div v-for="(item, index) in MesList" :key="index">
                                    <div class="re_box_item" @click="openUrl(item.id)"> 
                                        <div class="re_title">
                                            <div class="flex align-center">
                                                <div class="dian"></div>
                                                <span class="one-line-ellipsis rm-span">{{ item.title }}</span>
                                            </div>
                                            <span class="one-line-ellipsis" style="color: #999999;">{{
                                                item.release_time_text.slice(5, 10) }}</span>
                                        </div>
                                    </div>
                                    <div v-if="index < MesList.length - 1"
                                        style="width: 95%;height: 1px;background-color: #EEEEEE;margin: 0 auto;"></div>
                                </div>
                            </div>
                            <div style="width: 35%;background-color: #ffffff;">
                                <!-- <div style="width: 100%;height: 4px;background-color: #338CDE;"></div>
                                <div style="display: flex;justify-content: space-between;align-items: center;">
                                    <div style="display: flex;align-items: center;gap: 10px;padding: 13px;">
                                        <div>
                                            <img src="/img/f2.png" alt="" style="width: 35px;"></img>
                                        </div>
                                        <div
                                            style="padding-left: 10px;font-family: PingFang SC;font-weight: bold;font-size: 22px;color: #323232;">
                                            国务院百事通
                                        </div>
                                    </div>
                                    <div class="pr-[20px]" @click="toMore()"
                                        style="display: flex;align-items: center;cursor: pointer;">
                                        <span style="color: #999999;">更多</span>
                                        <img src="/img/index/more_j.png" alt=""
                                            style="width: 8px;height: 13px;margin-left: 5px;"></img>
                                    </div>
                                </div> -->
                                <div style="width: 100%;height: 1px;background-color: #EEEEEE;"></div>
                                <div v-for="(item, index) in YwList" :key="index">
                                    <div class="re_box_item" @click="openUrl(item.id)">
                                        <div class="re_title">
                                            <div class="flex align-center">
                                                <div class="dian"></div>
                                                <span class="one-line-ellipsis gw-span">{{ item.title }}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div v-if="index < YwList.length - 1"
                                        style="width: 95%;height: 1px;background-color: #EEEEEE;margin: 0 auto;"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>

            </div>

            <AppFooter></AppFooter>
            <!-- 右分享 -->
            <!-- <div style="position: absolute;top: 300px;right:36px;" class="flex flex-col fiximg">
            <img :src="`/img/index/douyin.png`" alt="">
            <img :src="`/img/index/wb.png`" alt="">
            <img :src="`/img/index/wx.png`" alt="">
            <img :src="`/img/index/blbl.png`" alt="">
        </div> -->
            <!-- 底部鼠标 -->
            <div class="mouse_img">
                <img :src="`/img/index/hmouse.png`" alt="">
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { defineEmits } from 'vue'
import { NPagination } from 'naive-ui'
import $api from '@/service/webRequest'
import emitter from '@/plugins/emitter'
const inputSearch = ref('');
const MesList = ref([]);
const newsListtyp = ref([]);
const page = ref(1);
const total = ref(0);
const cateid = ref(1);
const detailId = ref(null);
const showCode = ref(0);
const showCodeDo = (id: number) => {
    showCode.value = id;
}
// 信息公开
onMounted(() => {
    getFwImg();
    getMesList();
    getYwList();
    //getNewstypeList();
})
const hmScroll = () => {
    let home = document.getElementById('regid');
    if (home.scrollTop + home.clientHeight >= home.scrollHeight) {
        emit('toSwpe', 6);
    }
    if (home.scrollTop == 0) {
        emit('toSwpe', 4);
    }
}
const fwImg = ref([]);
//服务青年轮播图
const getFwImg = async () => {
    const res = await $api.get('/api/index/images?page=1&limit=9&status=2')
    fwImg.value = res.data.data.list;
}
const openWeb = (item) => {
    //out 外链   in内部
    if (item.type == 'out') {
        window.open(item.url);
    } else {
        window.open(`/info/${item.url}?type=1`)
    }
    console.log(item)
}
const emit = defineEmits(['toSwpe'])

const toMore = () => {
    window.open(`/messageBoard`)
}
const toHome = (index: number) => {
    emit('toSwpe', index);
}
//跳转详情
const openUrl = (id: number) => {
    window.open(`/info/${id}?type=3`)
}
//跳转详情
// const gotoDetail = (id: number) => {
//     detailId.value = {
//         id: id,
//         type: '1'
//     };
//     localStorage.setItem('detailId', JSON.stringify(detailId.value));
//     emit('toSwpe',6)
//     emitter.emit('detailId', detailId.value);
// }
//搜索
const toSearch = () => {
    // emit('toSwpe',9);
    // emitter.emit('inputSea', {
    //     keywords:inputSearch.value,
    //     type: 1,
    // });
    window.open(`/search_info?type=1&keywords=${inputSearch.value}`)
}
const getMesList = async () => {
    const res = await $api.post('/api/home.encyclopedia/index',
        {
            cate_id: '102',
            limit: 7,
            page: 1,
        }
    )
    MesList.value = res.data.data.list;
}
const YwList = ref([]);
const getYwList = async () => {
    const res = await $api.post('/api/home.encyclopedia/index',
        {
            cate_id: '103',
            limit: 7,
            page: 1,
        }
    )
    YwList.value = res.data.data.list;
}
const getNewstypeList = async () => {
    const res = await $api.get('/api/home.news/cate?isnews=1')
    newsListtyp.value = res.data.data.list;
}
</script>

<style lang="scss" scoped>
@import '@/assets/index.scss';


/* pc端样式 */

.rightcon {
    width: 100%;
    height: 100%;
    background-image: url('/img/index/rightcons.png');
    background-size: 100% 100%;
    overflow-y: auto;

    .fiximg {
        img {
            margin-bottom: 20px;
        }
    }
}

.top_search {
    margin: 0 auto;
    margin-top: 50px;
    height: 70px;
    justify-content: space-between;
    align-items: center;
    margin-left: 415px;
    width: 1200px;
}

.inputform {
    position: relative;
    width: 340px;
}

.contactInput {
    width: 337px;
    height: 50px;
    background: #FFFFFF;
    border-radius: 8px;
    border: 1px solid #DEE4E8;
    padding-left: 15px;
}

.contactInput::placeholder {
    color: #999999;
}

.contactInput:focus {
    outline: none;
}

.search_rinput {
    width: 68px;
    height: 50px;
    background: #338CDE;
    border-radius: 0px 8px 8px 0px;
    position: absolute;
    top: 0;
    right: 0;
    display: flex;
    justify-content: center;
    align-items: center;
}

.News_box {
    width: 1300px;
    margin: 0 auto;

    .lef_box {
        height: 100%;
        margin-top: 40px;
        margin-left: 100px;

    }
}

.le1 {
    color: #338CDE;
    font-family: PingFang SC;
    font-size: 22px;
    font-weight: 800;
    width: 261px;
    height: 65px;
    line-height: 65px;
    text-align: center;
    margin-bottom: 20px;
    cursor: pointer;
    border: 1px solid #338CDE;
    background-image: url('/img/ly_listbg.png');
    background-size: 100% 100%;
}

.le2 {
    color: #323232;
    font-family: PingFang SC;
    font-size: 22px;
    font-weight: 400;
    width: 261px;
    height: 65px;
    background-color: #ffffff;
    line-height: 65px;
    text-align: center;
    margin-bottom: 20px;
    cursor: pointer;
    border: 1px solid transparent;
}

.le2:hover {
    border: 1px solid #338CDE;
}

.ly_list {
    width: 298px;
    height: 298px;
    background-color: #ffffff;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    position: relative;
}

.re_box_item {
    width: 100%;
    padding: 15px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .re_title {
        width: 100%;
        font-family: Microsoft YaHei;
        font-weight: 400;
        font-size: 16px;
        color: #323232;
        display: flex;
        align-items: center;
        justify-content: space-between;
        cursor: pointer;

        .dian {
            width: 10px;
            height: 10px;
            background: #3B90DF;
            border-radius: 50%;
            margin-right: 15px;
        }
    }

    // &:hover{
    //     background-image: url('/img/index/listhmbg.png');
    //     background-size: 100% 100%;
    //     cursor: pointer;
    //     .slices_rol{
    //         width: 10px;
    //         height: 88px;
    //         background: #FAA828;
    //         position: absolute;
    //         right: 0;
    //     }
    // }

}

.rm-span {
    width: 550px;
}

.gw-span {
    width: 350px;
}

@media (max-width: 1440px) {
    .top_search {
        width: 920px;
        margin-left: 373px;

    }

    .News_box {
        width: 1032px;
        margin-left: 270px;
    }

    .rig_box {
        width: 700px;
    }

    .rm-span {
        width: 450px;
    }

    .gw-span {
        width: 280px;
    }
}
@media (max-width: 1366px) {
    .top_search {
        width: 870px;
        margin-left: 357px;

    }

    .News_box {
        width: 970px;
        margin-left: 260px;
    }

    .rig_box {
        width: 700px;
    }

    .rm-span {
        width: 385px;
    }

    .gw-span {
        width: 225px;
    }
}
@media (max-width: 1280px) {
    .top_search {
        width: 800px;
        margin-left: 357px;

    }

    .News_box {
        width: 900px;
        margin-left: 260px;
    }

    .rig_box {
        width: 700px;
    }

    .rm-span {
        width: 385px;
    }

    .gw-span {
        width: 225px;
    }
}
</style>
