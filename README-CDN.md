# 团市委官网3.0 - CDN图片处理指南

## 静态生成模式下的CDN图片处理

本项目已配置为支持在使用 `nuxt generate` 静态生成模式下自动处理图片CDN路径。以下是相关功能和使用方法的说明。

### 配置说明

1. CDN基础URL已配置为 `https://cdn.web.0rui.cn/`
2. 在 `nuxt.config.ts` 中已添加相关配置
3. 创建了专用的CDN图片处理插件和组件

### 使用方法

#### 方法一：使用CdnImage组件（推荐）

```vue
<template>
  <!-- 使用CdnImage组件自动处理CDN路径 -->
  <CdnImage src="public/img/logo.png" class="image_1" referrerpolicy="no-referrer" />
</template>
```

#### 方法二：使用全局$cdnImage方法

```vue
<template>
  <img :src="$cdnImage('public/img/logo.png')" class="image_1" referrerpolicy="no-referrer" />
</template>

<script setup>
const { $cdnImage } = useNuxtApp()
</script>
```

### 路径处理规则

- 以 `public/` 开头的路径会自动转换为 `https://cdn.web.0rui.cn/img/...`
- 已经是完整URL的路径（以http://或https://开头）不会被修改
- 其他路径会直接拼接CDN基础URL

### 开发与生产环境

- 在开发环境中，默认使用本地路径，不进行CDN转换
- 在生产环境（静态生成）中，自动应用CDN路径
- 如需在开发环境中测试CDN路径，可以使用 `<CdnImage src="..." :forceCdn="true" />` 强制启用CDN

### 静态生成命令

```bash
# 生成静态网站
npm run generate

# 预览生成的静态网站
npm run preview
```

生成的静态文件将位于 `.output/public` 目录中，可以部署到任何静态文件服务器。