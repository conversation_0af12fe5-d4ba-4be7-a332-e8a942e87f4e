<template>
	<div style="width: 100%;height: 100%;">
		<img :src="`/img/index/logo.ong`" alt="LOGO" style="display: none;">
		<!-- Swiper 容器 -->
		<div :class="['group_666', { 'isOldfont': isOldfont }]" style="position: relative;height: 100vh;">
			<AppHeader v-if="swiper_exp > 0" @crrentTop="toswiper" @openOldMode="openOldMode" :is-old="isOldfont"
				ref="AppHeader" :crrent-num="swiper_exp" />
			<swiper class="swiper-container h-full" @swiper="onSwiperNews" v-bind="swiperOptionsNews">
				<swiper-slide>
					<img :src="`/img/index/homebg.png`" style="width: 100%;height: 100%;object-fit: cover;">
					<div style="position: absolute;top:15%;left:0px;right: 0px;">
						<img :src="`/img/index/dlogo1.png`" class="home-logo" style="margin: 0 auto;" alt="">
					</div>
					<div style="position: absolute;top: 53%;left:0px;right: 0px;">
						<img :src="`/img/index/hmouse1.png`" style="width: 180px;margin: 0 auto;" alt="">
					</div>
				</swiper-slide>

				<!-- 首页 -->
				<swiper-slide>
					<AppHome v-if="swiper_exp == 1" @toSwpe="toswiper"></AppHome>
				</swiper-slide>

				<!-- 关于我们 -->
				<swiper-slide>
					<AppAbout v-if="swiper_exp == 2" @toSwpe="toswiper"></AppAbout>
				</swiper-slide>

				<!-- 新闻动态 -->
				<swiper-slide>
					<AppNews v-if="swiper_exp == 3" @toSwpe="toswiper"></AppNews>
				</swiper-slide>

				<!-- 青春洛阳 -->
				<swiper-slide>
					<AppLuoYang v-if="swiper_exp == 4" @toSwpe="toswiper"></AppLuoYang>
				</swiper-slide>

				<!-- 服务青年 -->
				<swiper-slide>
					<AppRegiment v-if="swiper_exp == 5" @toSwpe="toswiper"></AppRegiment>
				</swiper-slide>

				<!--信息公开-->
				<swiper-slide>
					<AppMessage v-if="swiper_exp == 6" @toSwpe="toswiper"></AppMessage>
				</swiper-slide>
				<!-- 新闻动态详情 -->
				<!-- <swiper-slide :show="isNews" id="newsid">
					<AppNewsdel ></AppNewsdel>
				</swiper-slide> -->
				<!-- 关于我们详情 -->
				<!-- <swiper-slide  :show="isAbout" id="aboutid">
					<AppAboutdel></AppAboutdel>
				</swiper-slide> -->
				<!-- 留言板 -->
				<swiper-slide>
					<AppContactUs v-if="swiper_exp == 7" @toSwpe="toswiper"></AppContactUs>
				</swiper-slide>
				<!-- 搜索详情 -->
				<!-- <swiper-slide :show="isSearch" id="searchid">
					<AppSearchdel @toSwpe="toswiper"></AppSearchdel>
				</swiper-slide> -->
			</swiper>

			<!-- <div style="position: absolute;width: 100%;z-index: 100;">
				<div class="flex-row justify-center align-center">
					
					<div style="position: absolute;bottom: 30px;right:30px;">
						<img :src="`/img/index/mouse.png`" alt="">
					</div>
					
				</div>
			</div> -->
			<!-- 右分享 -->
			<div v-if="swiper_exp > 0" ref="targetElement" class="flex flex-col fiximg">
				<!-- <img src="/img/qiqiu.png" class="qqt" @click="updateRight" alt=""></img> -->
				<div style="position: absolute;top: 200px;">
					<div class="divcode">
						<img class="imgs" :src="`/img/index/douyin.png`" alt="">
						<p class="pcode">
							<img :src="`/img/dycode.jpg`" alt="">
						</p>
					</div>
					<div class="divcode">
						<img class="imgs" :src="`/img/index/wb.png`" alt="">
						<p class="pcode">
							<img :src="`/img/wbcode.jpg`" alt="">
						</p>
					</div>
					<div class="divcode">
						<img class="imgs" :src="`/img/index/wx.png`" alt="">
						<p class="pcode">
							<img :src="`/img/wxcode.jpg`" alt="">
						</p>
					</div>
					<div class="divcode">
						<img class="imgs" :src="`/img/index/blbl.png`" alt="">
						<p class="pcode">
							<img :src="`/img/bilicode.jpg`" alt="">
						</p>
					</div>
					<!-- <div class="tuanguanxi">
						<a href="https://mp.weixin.qq.com/s/N-qAWMJNGb7WjkXp3Xqrhg" rel="nofollow" target="_blank"
							style="display: block;"><img src="/img/tgxzj.jpg"></a>
						<a href="http://qnyz.lyd.com.cn/" rel="nofollow" target="_blank" style="display: block;"><img
								src="/img/qnyz.jpg"></a>
						<img src="/img/lyb.jpg" @click="openUrl" />
					</div> -->
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { Swiper, SwiperSlide } from 'swiper/vue'
import { nextTick, ref } from 'vue'
import 'swiper/css'
import { Autoplay, Navigation, Pagination, Scrollbar, A11y, EffectCoverflow, EffectFade, Mousewheel } from "swiper/modules";
import ScrollReveal from 'scrollreveal';
import { NNumberAnimation } from 'naive-ui'

import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import 'swiper/css/effect-coverflow';
import 'swiper/css/mousewheel'
import 'swiper/css/grid'
import 'swiper/css/effect-fade'
import 'swiper/css/autoplay'
import * as swiperAni from '@/assets/animate/animate.js'
import $api from '@/service/webRequest'
import { useI18n } from 'vue-i18n'
import { useStore } from '~/store'
const cdnUrl = useCdn()
const isOldfont = ref(false);
const { locale } = useI18n();
const openUrl = () => {
	window.open(`/messageBoard/`)
}
const rightValue = ref('100px');
const updateRight = () => {
	if (rightValue.value == '34px') {
		rightValue.value = '-94px';
	} else {
		rightValue.value = '34px';
	}
}

const openOldMode = (value: boolean) => {
	isOldfont.value = !isOldfont.value
}
let newsSwiper: any = null
const onSwiperNews = (swiper: any) => {
	newsSwiper = swiper
	swiper.on('slideChange', () => {
		// 更新当前活动幻灯片索引
		// isMeassage.value = false;
		console.log(swiper.realIndex);
		swiper_exp.value = swiper.realIndex;
		// newsSwiper.mousewheel.disable();
		// newsSwiper.allowTouchMove = true;// 开启拖动 
		if (swiper.realIndex != 0) {
			newsSwiper.mousewheel.disable();
			newsSwiper.allowTouchMove = true;// 开启拖动 
		} else {
			newsSwiper.mousewheel.enable();
			newsSwiper.allowTouchMove = true;
		}
		// if (swiper.realIndex == 1) {
		// 	newsSwiper.mousewheel.disable();
		// 	newsSwiper.allowTouchMove = true;// 开启拖动 
		// }
		// else if (swiper.realIndex == 6 || swiper.realIndex == 9 || swiper.realIndex == 7 || swiper.realIndex == 8) {
		// 	newsSwiper.mousewheel.disable(); //禁止鼠标滑轮控制
		// 	newsSwiper.allowTouchMove= false;//关闭拖动
		// }
		// else {
		// 	newsSwiper.mousewheel.enable(); //开启鼠标滑轮控制
		// 	newsSwiper.allowTouchMove = true;// 开启拖动 
		// }
	})
}
// let isMeassage = ref(false);
// let isNews = ref(false);
// let isAbout = ref(false);
// let isSearch = ref(false);
//跳转每页swiper的方法
const toswiper = (index: number) => {
	// if (index == 6) isNews.value = true;
	// if (index == 7) isAbout.value = true;
	// if (index == 8) isMeassage.value = true;
	// if (index == 9) isSearch.value = true;
	//如果是跨了一页就为0 否则为1000
	if (index > newsSwiper.realIndex) {
		if (newsSwiper.realIndex + 1 != index) {
			newsSwiper.slideTo(index, 0, true)
		} else {
			newsSwiper.slideTo(index, 800, true)
		}
	} else {
		if (newsSwiper.realIndex - 1 != index) {
			newsSwiper.slideTo(index, 0, true)
		} else {
			newsSwiper.slideTo(index, 800, true)
		}
	}
}
const swiper_exp = ref(0);

const swiperOptionsNews = {
	autoplay: {
		delay: 11000,
		disableOnInteraction: false,
	},
	noSwipingSelector: '.no-wrap',  // 指定不接管该区域的滑动事件
	// autoHeight : true,
	direction: 'horizontal',
	mousewheel: {
		releaseOnEdges: true,
		sensitivity: 0.5
	},
	slidesPerView: 1,
	speed: 500,
	// lazy: {
	// 	loadPrevNext: true,
	// },
	// effect: 'fade',
	// loop: true,
	modules: [Mousewheel],
	// navigation: {
	// 	nextEl: '#swipen_prev',
	// 	prevEl: '#swipen_next',
	// },
}

// let vesSwiper: any = null
// const onSwiperExp = (swiper: any) => {
// 	vesSwiper = swiper
// 	// 监听幻灯片变化事件
// 	swiper.on('slideChange', () => {
// 		// 更新当前活动幻灯片索引
// 		activeSlideIndex.value = swiper.realIndex % 3
// 	})
// }

// const activeSlideIndex = ref(0);

const router = useRouter()

onMounted(() => {
	const coMobile = /Mobi|Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

	if (coMobile) {
		router.push('/phone_index/')
	} else {
		router.push('/')
	}
	// let indexs_to = localStorage.getItem('index_detail')
	// if (indexs_to) {
	// 	toswiper(Number(indexs_to));
	// 	swiper_exp.value = Number(indexs_to);
	// 	localStorage.removeItem('index_detail')
	// }
	nextTick(() => {
		// 初始化 ScrollReveal
		// setTimeout(() => {
		// 	animate()
		// }, 500)
	})
})

// const animate = () => {

// 	const sr = ScrollReveal();
// 	sr.reveal('.leftBoxTop', {
// 		origin: "left",
// 		distance: "1000px",
// 		duration: 1300,
// 		delay: 100,
// 		opacity: 0,
// 		scale: 0.9,
// 		reset: true,
// 		mobile: true,
// 	})
// 	sr.reveal('.leftBox', {
// 		origin: "left",
// 		distance: "1000px",
// 		duration: 1300,
// 		delay: 100,
// 		opacity: 0,
// 		scale: 0.9,
// 		reset: true,
// 		mobile: true,
// 	})
// 	sr.reveal('.rightBox', {
// 		origin: "right",
// 		distance: "1000px",
// 		duration: 1300,
// 		delay: 100,
// 		opacity: 0,
// 		scale: 0.9,
// 		reset: true,
// 		mobile: true,
// 	})
// 	sr.reveal('.topBox', {
// 		origin: "top",
// 		distance: "1000px",
// 		duration: 1300,
// 		delay: 100,
// 		opacity: 0,
// 		scale: 0.9,
// 		reset: true,
// 		mobile: true,
// 	})
// 	sr.reveal('.bottomBox', {
// 		origin: "bottom",
// 		distance: "1000px",
// 		duration: 1300,
// 		delay: 100,
// 		opacity: 0,
// 		scale: 0.9,
// 		reset: true,
// 		mobile: true,
// 	})
// 	sr.reveal('.numberTopBox', {
// 		origin: "top",
// 		distance: "1000px",
// 		duration: 1300,
// 		delay: 100,
// 		opacity: 0,
// 		scale: 0.9,
// 		reset: true,
// 		mobile: true,
// 		beforeReveal: function (el: any) {
// 			// numberAnimationInstRef.value.play()
// 			// numberAnimationInstRefKH.value.play()
// 			// numberAnimationInstRefJS.value.play()
// 			// numberAnimationInstRefHY.value.play()
// 		},
// 	})
// 	sr.reveal('.group_29 ', {
// 		origin: "bottom",
// 		distance: "500px",
// 		opacity: 0,
// 		scale: 0.9,
// 		reset: false,
// 		mobile: true,
// 	})
// }

</script>

<style lang="scss" scoped>
@import "@/assets/animate/animate.min.css";
@import "@/assets/index.scss";

:deep(.dswper .swiper-slide-next) {
	transform: translate3d(0px, 0px, -507px) rotateX(0deg) rotateY(0deg) scale(1) !important;
}

:deep(.dswper .swiper-slide-prev) {
	transform: translate3d(0px, 0px, -507px) rotateX(0deg) rotateY(0deg) scale(1) !important;
}

.group_666 .swiper-slide {
	width: 100%;
	height: auto;
	// transition: 1s linear 2s;
	// transform: scale(1.1, 1.1);
}

// .group_666 .swiper-slide-active img,
// .swiper-slide-duplicate-active img {
// 	transition: 6s linear;
// 	transform: scale(1, 1);
// }

.bottom_imgs {
	background: var(--qall) no-repeat;
	width: 1230px;
	height: 272px;
	background-size: 100% 100%;
	margin: 0 auto;

}

.content_box {
	background: var(--m1);
	width: 561px;
	height: 521px;
	z-index: 1;
	position: absolute;
	right: -12px;
	bottom: -13px;
	background-size: 100%;
}

.contactForm {
	width: 100%;

	.inputform {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 1200px;
	}

}



#myTextarea {
	width: 1200px;
	height: 167px;
	background: #FFFFFF;
	border-radius: 6px 6px 6px 6px;
	opacity: 0.8;
	padding-left: 20px;
	padding-top: 20px;
	resize: none;
}

#myTextarea::placeholder {
	color: #768597;
}

#myTextarea:focus {
	outline: none;
}

.char-count {
	position: absolute;
	bottom: 5px;
	/* 根据需要调整距离底部的位置 */
	right: 10px;
	/* 根据需要调整距离右侧的位置 */
	font-size: 12px;
	/* 根据需要调整字体大小 */
	color: #A8CBFF;
}

@media (max-width: 768px) {
	.contactInput {
		font-size: 18px;
		height: 40px;
	}
}

.contactBut {
	width: 108px;
	border-radius: 4px 4px 4px 4px;
	position: absolute;
	bottom: 20px;
	right: 30px;
	height: 36px;
	background-color: #222222;
	border: none;
	font-weight: 400;
	font-size: 16px;
	line-height: 28px;
	color: #FFFFFF;
}

.pop {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: rgba(0, 0, 0, .8);
	z-index: 200;
	display: none;
}

.popCont {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
}

.pop_video_close {
	border-radius: 50%;
	position: absolute;
	top: 0;
	right: -75px;
	width: 50px;
	height: 50px;
	background: var(--close) no-repeat 0 0;
	background-size: 100% auto;
	border: 6px solid #979797;
}

.pop_video_cont {
	width: 600px;
	border: 6px solid #979797;
	background: #000;
}

.pop_video_cont video {
	display: block;
	width: 100%;
	height: 100%;
}

.hy_my_home {
	width: 585px;
	height: 300px;
	margin: 0 auto;
	transition: all 0.3s ease;

	&:hover {
		transform: scale(1.05);
		cursor: pointer;
	}
}

.news_my_home {
	cursor: pointer;

	&:hover .news_title_home {
		color: coral;
	}
}

.news_title_home {
	font-size: 18px;
	font-weight: 700;
	padding-top: 10px;
}

.fiximg {
	width: 80px;
	height: 100%;
	transition: right 0.5s ease-in-out;
	position: fixed;
	top: 40px;
	align-items: center;
	z-index: 111;
	right: 100px;

	.qqt {
		cursor: pointer;
	}

	.divcode {
		position: relative;
		display: flex;
		justify-content: center;

		.imgs {
			width: 63px;
			margin-bottom: 20px;
			cursor: pointer;
		}

		.pcode {
			display: inline-block;
			position: absolute;
			width: 0;
			right: 99px;
			bottom: 20px;
			transition: all 0.5s;
		}

		&:hover .pcode {
			width: 100px;
		}
	}

	.tuanguanxi {
		position: relative;
		left: 30px;
	}
}

.isOldfont * {
	font-size: 28px !important;
}

.home-logo {
	width: 300px;
}

@media screen and (max-width: 1366px) {
	.home-logo {
		width: 250px;
	}
}

@media screen and (max-width: 1440px) {
	.fiximg {
		right: 30px;
	}
}
</style>
