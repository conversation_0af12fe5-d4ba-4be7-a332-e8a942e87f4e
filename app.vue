<template>
  <n-config-provider :locale="zhCN">
    <!-- <AppHeader /> -->
    <NuxtPage />
    <!-- <AppFooter /> -->
  </n-config-provider>
</template>

<script lang="ts" setup>
import { NConfigProvider } from 'naive-ui'
import { zhCN, dateZhCN } from 'naive-ui'
import $api from '@/service/webRequest'
import { useStore } from '~/store'
import { useI18n } from 'vue-i18n'
const { locale } = useI18n()
useSeoMeta({
  title: '洛阳青年之家 | 共青团洛阳市委员会 - 引领青年成长，服务洛阳发展',
  ogTitle: '共青团洛阳市委员会官网 | 洛阳青年工作动态与服务平台',
  description: '共青团洛阳市委员会权威平台，发布青年政策、志愿服务、就业创业指南、主题活动资讯，助力洛阳青年发展与城市建设。',
  ogDescription: '获取洛阳共青团最新工作动态、青年活动报名入口、权益维护服务及社会实践机会，加入洛阳青年成长社区。',
  ogImage: 'https://tsw.hschool.com.cn/web/img/index/logo.png',
  twitterCard: 'summary_large_image',
  // 补充关键元标签 
  keywords: '洛阳共青团,青年志愿服务,洛阳就业创业,青少年权益保护,青年大学习,洛阳青年活动'
})
// 补充企业微信专用标签
useHead({
  meta: [
    { name: 'wechat:image', content: 'https://tsw.hschool.com.cn/web/img/index/logo.png'  },
    { name: 'x5:image', content: 'https://tsw.hschool.com.cn/web/img/index/logo.png'  },
  ]
})
const store = useStore();
onMounted(() => {
})

</script>

<style lang="scss">
#__nuxt {
  height: 100%;
  width: 100%;
}

html,
body {
  scroll-behavior: smooth;
  position: relative;
  height: 100%;
}

body {
  background: #eee;
  font-family: Helvetica Neue, Helvetica, Arial, sans-serif;
  font-size: 14px;
  color: #000;
  margin: 0;
  padding: 0;
}

::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}

::-webkit-scrollbar-track {
  background: linear-gradient(90deg, #434343, #434343 1px, #111 0, #111);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #434343, #111);
  border-radius: 6px;
  box-shadow: inset 2px 2px 2px rgba(255, 255, 255, 0.25),
    inset -2px -2px 2px rgba(0, 0, 0, 0.25);
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #e52e71, #ff8a00);
}

select{
  -webkit-appearance:none;
  box-shadow: none;
-moz-appearance:none;

appearance:none;
}
.mouse_img {
    position: absolute;
    bottom: 30px;
    right: 60px;
    width: 150px;
}
@media (max-width: 1440px) {
  .mouse_img {
        width: 100px;
        right: 15px;
    }
}
</style>
