<template>
    <!-- PC端布局 -->
    <div class="flex justify-between w-full h-full">
        <!-- 左边导航栏 -->


        <!-- 右边内容栏 -->
        <div class="rightcon" id="contid" @scroll="hmScroll">
            <div class="flex top_search ">
                <div style="width: 350px;">
                    <img @click="toMore(0)" :src="`/img/index/dlogo.png`"  style="cursor: pointer;;width: 100%;height: 100%;object-fit: cover;">
                </div>
                <div class="inputform">
                    <input v-model="inputSearch" class="contactInput w-full md:w-[337px]" type="text"
                        placeholder="请输入搜索关键字" />
                    <div class="search_rinput" @click="toSearch">
                        <img src="/img/index/hmsearch.png" alt="">
                    </div>
                </div>
            </div>
            <div class=" w-full h-full left-msg">

                <div
                    style="position: relative;;z-index: 1;background: #ffffff;padding: 30px;border-radius: 16px;margin-top: 20px;margin-left: 100px;">
                    <img class="to_img" :src="`/img/tt.png`" alt="">
                    <div class="liuyanba"></div>
                    <form class="contactForm w-full" @submit.prevent="submitForm">
                        <div class="inputforms">
                            <span>姓名</span>
                            <input v-model="formData.name"
                                class="contactInputs  mt-[15px] md:mt-[30px] w-full md:w-[600px]" type="text"
                                placeholder="请输入您的姓名" />
                            <span>电话</span>
                            <input v-model="formData.mobile"
                                class="contactInputs mt-[15px] md:mt-[30px] w-full md:w-[600px]" type="text"
                                placeholder="请输入电话" />
                            <span>问题</span>
                            <input v-model="formData.question"
                                class="contactInputs mt-[15px]  md:mt-[30px] w-full md:w-[600px]" type="text"
                                placeholder="请输入问题" />
                        </div>
                        <div class="relative mt-4 md:mt-8 w-full md:w-[1100px] flex">
                            <span style="font-size: 18px;font-weight: 400;width: 60px;padding-right: 10px;">描述</span>
                            <textarea v-model="formData.message" id="myTextarea" placeholder="为了更好地帮助您，请尽量提供详细的信息"
                                rows="5" maxlength="100"></textarea>
                        </div>
                        <button type="submit" class="contactBut">
                            {{ '提交' }}
                        </button>
                    </form>
                </div>
                <div class="re_top">
                    <div class="re_col" style="margin-left: 100px;"></div>
                    <div class="re_tit">精选留言</div>
                    <div class="re_col"></div>
                </div>
                <div class="re_list_top ml-[100px]" style="gap: 20px;">
                    <div class="re_list" v-for="item, index in liuyanList">
                        <div style="margin: 30px;">
                            <div class="re_list_tit flex" style="width: 100%;">
                                <span style="width: 17%">问题：</span>
                                <span style="color: #323232;width:85% ;">{{ item.question }}</span>
                            </div>
                            <div class="re_list_tit flex" style="margin-top: 15px;width: 100%;">
                                <span style="width: 17%">描述：</span>
                                <span :title="item.message" class="three-line-ellipsis"
                                    style="font-weight: 400;font-size: 14px;color: #999999;width:85% ;">{{ item.message
                                    }}</span>
                            </div>
                            <div class="re_list_tit flex" style="margin-top: 15px;">
                                <span style="color: #368FDF;width: 17%;">回复：</span>
                                <span :title="item.answer" class="two-line-ellipsis"
                                    style="font-weight: 400;font-size: 14px;color: #999999;width:85% ;">{{ item.answer
                                    }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <AppFooter></AppFooter>
            <!-- 右分享 -->
            <!-- <div style="position: absolute;top: 300px;right:36px;" class="flex flex-col fiximg">
            <img :src="`/img/index/douyin.png`" alt="">
            <img :src="`/img/index/wb.png`" alt="">
            <img :src="`/img/index/wx.png`" alt="">
            <img :src="`/img/index/blbl.png`" alt="">
        </div> -->
            <!-- 底部鼠标 -->
            <div class="mouse_img">
                <img :src="`/img/index/hmouse.png`" alt="">
            </div>
        </div>
       
    </div>
   
</template>

<script lang="ts" setup>
import { defineEmits } from 'vue'
import $api from '@/service/webRequest'
const inputSearch = ref('');
// 表单数据
const formData = reactive({
    name: '',
    mobile: '',
    question: '',
    message: ''
})
const total = ref(0);
const liuyanList = ref([]);

const getLiuyanList = async () => {
    const res = await $api.post('/api/home.leave_word/index',
        {
            limit: 4,
            page: 1,
            status: 2,
            show: 1

        }
    )
    total.value = res.data.data.count;
    liuyanList.value = res.data.data.list;
}
onMounted(() => {
    getLiuyanList();
})
const toSearch = () => {
    // emit('toSwpe',9);
    // emitter.emit('inputSea', {
    //     keywords:inputSearch.value,
    //     type: 1,
    // });
    window.open(`/search_info?type=1&keywords=${inputSearch.value}`)
}
const emit = defineEmits(['toSwpe'])
const toMore = (index:number) => {
    emit('toSwpe', index);
}
const hmScroll = () => {
    let home = document.getElementById('contid');
    console.log(home.scrollTop);
    if (home.scrollTop == 0) {
        emit('toSwpe', 6);
    }
}
// 表单方法
const submitForm = () => {
    console.log(formData);

    // 这里可以添加表单验证逻辑
    if (!formData.name) {
        alert('请输入您的姓名')
        return
    }

    if (!formData.mobile) {
        alert('请输入您的手机号码')
        return
    }

    if (!formData.question) {
        alert('请输入您的问题')
        return
    }

    if (!formData.message) {
        alert('请输入您的留言内容')
        return
    }
    $api.post("/api/home.leave_word/add", formData)
        .then((res: any) => {
            console.log(res)
            if (res.status == 200) {
                alert('留言成功')
            } else {
                alert('失败')
            }
            formData.name = '';
            formData.mobile = '';
            formData.question = '';
            formData.message = '';
        })
        .catch((err) => {
            console.dir(err)
        })
    // 重置表单
    // formData.name = ''
    // formData.mobile = ''
    // formData.content = ''
}
</script>

<style lang="scss" scoped>
@import '@/assets/index.scss';

/* pc端样式 */
.rightcon {
    width: 100%;
    height: 100%;
    background-image: url('/img/index/rightcons.png');
    background-size: 100% 100%;
    overflow-y: auto;
    overflow-x: hidden;
}

.top_search {
    margin: 0 auto;
    margin-top: 50px;
    height: 70px;
    justify-content: space-between;
    align-items: center;
    margin-left: 415px;
    width: 1200px;
}

.inputform {
    position: relative;
    width: 340px;
}

.contactInput {
    width: 337px;
    height: 50px;
    background: #FFFFFF;
    border-radius: 8px;
    border: 1px solid #DEE4E8;
    padding-left: 15px;
}

.contactInput::placeholder {
    color: #999999;
}

.contactInput:focus {
    outline: none;
}

.search_rinput {
    width: 68px;
    height: 50px;
    background: #338CDE;
    border-radius: 0px 8px 8px 0px;
    position: absolute;
    top: 0;
    right: 0;
    display: flex;
    justify-content: center;
    align-items: center;
}

//------------------------------------------
.liuyanba {
    /* height: 22px; */
    margin: 20px auto;
    font-family: Microsoft YaHei UI;
    font-weight: bold;
    font-size: 22px;
    color: #358DDE;
}

.contactForm {
    width: 100%;
    margin-left: 30px;

    .inputforms {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 1100px;

        span {
            color: #323232;
            font-size: 18px;
            width: 50px;
            margin-top: 30px;
            height: 100%;
            font-weight: 400;
        }
    }

}

.contactInputs {
    width: 380px;
    height: 50px;
    background: #FFFFFF;
    border-radius: 6px;
    padding-left: 20px;
    border: 1px solid #999999;
    margin-left: 20px;
    margin-right: 20px;
}

.contactInputs::placeholder {
    color: #768597;
}

.contactInputs:focus {
    outline: none;
}

#myTextarea {
    width: 1100px;
    height: 167px;
    background: #FFFFFF;
    border-radius: 6px 6px 6px 6px;
    opacity: 0.8;
    padding-left: 10px;
    padding-top: 10px;
    resize: none;
    border: 1px solid #999999;
}

#myTextarea::placeholder {
    color: #768597;
}

#myTextarea:focus {
    outline: none;
}

.contactBut {
    width: 140px;
    border-radius: 4px 4px 4px 4px;
    // position: absolute;
    // bottom: 20px;
    // right: 30px;
    height: 50px;
    margin-top: 20px;
    margin-left: 60px;
    background: linear-gradient(0deg, #338CDE 0%, #469CE2 100%);
    border: none;
    font-weight: bold;
    font-size: 18px;
    color: #FFFFFF;
}

.re_top {
    width: 100%;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 50px 0px 40px 0px;

    .re_tit {
        font-family: Microsoft YaHei;
        font-weight: bold;
        font-size: 36px;
        color: #348CDD;
        margin: 0 30px;
    }

    .re_col {
        width: 78px;
        height: 3px;
        background: #348CDD;
    }
}

.re_list_top {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
}

.re_list {
    height: 243px;
    background: #FFFFFF;
    border-radius: 8px;

    .re_list_tit {
        font-family: Microsoft YaHei;
        font-weight: bold;
        font-size: 18px;
        color: #FFA234;
    }
}

.left-msg {
    width: 1300px;
    margin: 0 auto;
    margin-top: 70px;
    height: 1050px;
}

.to_img {
    width: 275px;
    position: absolute;
    left: 0px;
    right: 0px;
    top: -13px;
    margin: 0 auto;
}

@media (max-width: 1440px) {
    .top_search {
        width: 920px;
        margin-left: 370px;

    }
    .left-msg {
        width: 1018px;
        margin: 0;
        margin-left: 270px;
        margin-top: 60px;
    }

    #myTextarea {
        width: 752px;
    }

    .contactInputs {
        width: 196px;
    }

    .inputforms {
        display: flex;
        justify-content: start !important;
        align-items: center;
    }

    .contactForm .inputforms span {
        width: 41px;
    }
}
@media (max-width: 1366px) {
    .top_search {
        width: 860px;
        margin-left: 357px;
    }
    .left-msg {
        width: 960px;
        margin: 0;
        margin-left: 260px;
        margin-top: 60px;
    }

    #myTextarea {
        width: 658px;
    }

    .contactInputs {
        width: 165px;
    }

    .inputforms {
        display: flex;
        justify-content: start !important;
        align-items: center;
    }

    .contactForm .inputforms span {
        width: 41px;
    }
}
@media (max-width: 1280px) {
    .top_search {
        width: 800px;
        margin-left: 357px;
    }
    .left-msg {
        width: 900px;
        margin: 0;
        margin-left: 260px;
        margin-top: 60px;
    }

    #myTextarea {
        width: 658px;
    }

    .contactInputs {
        width: 165px;
    }

    .inputforms {
        display: flex;
        justify-content: start !important;
        align-items: center;
    }

    .contactForm .inputforms span {
        width: 41px;
    }
}
</style>
