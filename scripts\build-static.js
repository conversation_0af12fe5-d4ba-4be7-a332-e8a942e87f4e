// 静态生成构建脚本
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// CDN基础URL
const CDN_BASE_URL = 'https://cdn.web.0rui.cn/';

// 输出目录
const OUTPUT_DIR = path.resolve(__dirname, '../.output/public');

// 执行静态生成
console.log('开始执行静态生成...');
try {
  // 执行nuxt generate命令
  execSync('npm run generate', { stdio: 'inherit' });
  console.log('静态生成完成！');
  
  // 检查输出目录是否存在
  if (!fs.existsSync(OUTPUT_DIR)) {
    console.error('错误：输出目录不存在！');
    process.exit(1);
  }
  
  console.log('开始处理HTML文件中的图片路径...');
  
  // 递归处理目录中的所有HTML文件
  processDirectory(OUTPUT_DIR);
  
  console.log('所有HTML文件处理完成！');
  console.log(`静态网站已生成到: ${OUTPUT_DIR}`);
  console.log('可以使用 npm run preview 预览生成的静态网站');
} catch (error) {
  console.error('构建过程中发生错误:', error);
  process.exit(1);
}

/**
 * 递归处理目录中的所有HTML文件
 * @param {string} dir 目录路径
 */
function processDirectory(dir) {
  const files = fs.readdirSync(dir);
  
  for (const file of files) {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      // 递归处理子目录
      processDirectory(filePath);
    } else if (file.endsWith('.html')) {
      // 处理HTML文件
      processHtmlFile(filePath);
    }
  }
}

/**
 * 处理HTML文件中的图片路径
 * @param {string} filePath HTML文件路径
 */
function processHtmlFile(filePath) {
  console.log(`处理文件: ${path.relative(OUTPUT_DIR, filePath)}`);
  
  let content = fs.readFileSync(filePath, 'utf8');
  
  // 替换HTML中的图片路径
  // 匹配 src="public/img/xxx.png" 模式
  content = content.replace(/src=\"(public\/img\/[^\"]+)\"/g, (match, p1) => {
    return `src=\"${CDN_BASE_URL}${p1.replace('public/', '')}\"`;
  });
  
  // 匹配 src=\"/public/img/xxx.png\" 模式
  content = content.replace(/src=\"(\/public\/img\/[^\"]+)\"/g, (match, p1) => {
    return `src=\"${CDN_BASE_URL}${p1.replace('/public/', '')}\"`;
  });
  
  // 匹配 src=\"/img/xxx.png\" 模式
  content = content.replace(/src=\"(\/img\/[^\"]+)\"/g, (match, p1) => {
    return `src=\"${CDN_BASE_URL}${p1.replace('/img/', '')}\"`;
  });
  
  // 匹配 src=\"img/xxx.png\" 模式
  content = content.replace(/src=\"(img\/[^\"]+)\"/g, (match, p1) => {
    return `src=\"${CDN_BASE_URL}${p1.replace('img/', '')}\"`;
  });
  
  // 移除_nuxt前缀的图片路径
  content = content.replace(/src=\"\/_nuxt\/img\/([^\"]+)\"/g, (match, p1) => {
    return `src=\"${CDN_BASE_URL}${p1}\"`;
  });
  
  // 匹配 src="/_nuxt/assets/images/xxx.png" 模式
  content = content.replace(/src=\"\/_nuxt\/assets\/images\/([^\"]+)\"/g, (match, p1) => {
    return `src=\"${CDN_BASE_URL}${p1}\"`;
  });
  
  fs.writeFileSync(filePath, content, 'utf8');
}