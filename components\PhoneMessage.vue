<template>
    <div class="w-full h-full" style="padding: 20px;">
        <div style="display: flex;justify-content: center;align-items: center;text-align: center;">
            <div><img class="re_top_img" :src="`/img/index/xxgk.png`" style="width: 80%;margin: 0 auto;"></img></div>
        </div>
        <div style="background-color: #ffffff;padding: 10px;margin-top: 20px;">
            <div style="padding-bottom: 10px;">
                <div class="dddccc">
                    <div v-for="(item, index) in newsListtyp" :key="index"
                        :class="['lef_boxitem2', { 'active': detailId == item.id }]"  @click="getMesList(item.id,1)">
                        <div>{{ item.name }}</div>
                        <div v-if="detailId == item.id"
                            style="height: 3px;background-color: #2C8AE0;width: 30px;margin: 0 auto;">
                        </div>
                    </div>
                </div>
            </div>
            <div v-for="(item, index) in MesList" :key="index" @click="openUrl(item.id)">
                <div class="re_box_item">
                    <div class="re_title">
                        <div class="flex align-center">
                            <div class="dian"></div>
                            <span class="one-line-ellipsis jc-span">{{ item.title }}</span>
                        </div>
                    </div>
                </div>
                <div v-if="index != 8" style="width: 95%;height: 1px;background-color: #EEEEEE;margin: 15px 0;"></div>
            </div>
            <div style="display: flex;justify-content:center;align-items: center;border: 1px solid #2C8AE0;height: 40px;margin-top: 20px;text-align: center;">
                <div @click="getPageList(0)" style="width: 34%;height: 40px;line-height: 40px;border-right: 1px solid #2C8AE0;background-color: #2C8AE0;color: #ffffff;">上一页</div>
                <div style="width: 33%;height: 40px;line-height: 40px;border-right: 1px solid #2C8AE0;">{{ page }} / {{ total }}</div>
                <div @click="getPageList(1)" style="width: 34%;height: 40px;line-height: 40px;background-color: #2C8AE0;color: #ffffff;">下一页</div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { defineEmits } from 'vue'
import { NPagination } from 'naive-ui'
import $api from '@/service/webRequest'
import emitter from '@/plugins/emitter'
const inputSearch = ref('');
const MesList = ref([]);
const newsListtyp = ref([]);
const page = ref(1);
const total = ref(0);
const cateid = ref(0);
const detailId = ref(0);

// 信息公开
onMounted(() => {
   // getMesList();
    getNewstypeList();
})

//跳转详情
const openUrl = (id: number) => {
    window.location.href=`/phone_info/${id}?type=2`;
}

const getPageList = async (type: number) => {
    page.value = type == 0 ? page.value - 1 : page.value + 1;
    if(page.value < 1){
        page.value = 1;
    }
    if(page.value > total.value){
        page.value = total.value;
    }
    getMesList(detailId.value);
}
const getMesList = async (id: number) => {
    detailId.value = id;
    const res = await $api.post('/api/home.information/index',
        {
            cate_id: detailId.value,
            limit: 7,
            page: page.value,
            isnews: 1
        }
    )
    //算出总页数
    total.value = Math.ceil(res.data.data.count / 7);
    MesList.value = res.data.data.list;
}
const getNewstypeList = async () => {
    const res = await $api.get('/api/home.information/cate')
    newsListtyp.value = res.data.data.list;
    getMesList(res.data.data.list[0].id);
}
</script>


<style lang="scss" scoped>
@import '@/assets/index.scss';

.re_box_item {
    width: 100%;
    box-sizing: border-box;
    padding: 0px 10px;
    margin-top: 15px;

    .re_title {
        width: 100%;
        font-family: Microsoft YaHei;
        font-weight: 400;
        font-size: 16px;
        color: #323232;
        display: flex;
        align-items: center;
        justify-content: space-between;
        cursor: pointer;
    }

    // &:hover{
    //     background-image: url('/img/index/listhmbg.png');
    //     background-size: 100% 100%;
    //     cursor: pointer;
    //     .slices_rol{
    //         width: 10px;
    //         height: 88px;
    //         background: #FAA828;
    //         position: absolute;
    //         right: 0;
    //     }
    // }
}

.dian {
    width: 10px;
    height: 10px;
    background: #3B90DF;
    border-radius: 50%;
    margin-right: 15px;
}
.dddccc {
    display: flex;
    align-items: center;
    justify-content: space-around;
    flex-wrap: nowrap;
    min-width: max-content;
    gap: 20px;
}
.jc-span{
    width: 100%;
}
.lef_boxitem2 {
    color: #323232;
    font-size: 16px;
    position: relative;
}

.active {
    color: #2C8AE0;
    font-weight: 600;
}
</style>
