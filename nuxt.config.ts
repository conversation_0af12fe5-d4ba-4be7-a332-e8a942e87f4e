// https://nuxt.com/docs/api/configuration/nuxt-config
import { defineNuxtConfig } from 'nuxt/config'

export default defineNuxtConfig({
  runtimeConfig: {
    public: {
      cdnDomain: 'https://cdn.web.0rui.cn'
    }
  },
  app: {
    baseURL:'/web/',
  },

  nitro: {
    routeRules: {
      '/**': { trailingSlash: false }
    }
  },

  router: {
    // extendRoutes(routes, resolve) {
    //   routes.push({
    //     name: 'index_info',
    //     path: '/params/:param1', // * 表示可选的多个参数
    //     component: resolve(__dirname, 'pages/index_info.vue')
    //   });
    // }
  },

  // 设置为false以启用客户端渲染，适合静态生成
  ssr: false,

  // 静态生成配置
  generate: {
    routes: [
      '/',
      '/aboutUs',
      '/proServices',
      '/proServices_con',
      '/societyDuty',
      '/societyDutyNew',
      '/concatUs',
      '/customerReviews'
    ]
  },

  css: [
    'swiper/css',
    'swiper/css/mousewheel',
     '@/assets/css/common.scss'
  ],

  devtools: { enabled: true },

  postcss: {
    plugins: {
      autoprefixer: {},
      tailwindcss: {},
    },
  },

  modules: ['@pinia/nuxt', 'nuxt-icons'],

  vite: {
    server: {
      proxy: {
        '/api': {
          target: "https://tsw.hschool.com.cn/api",
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, ''),
        },
        'http://**************/api': {
          target: "http://tsw.hschool.com.cn/api",
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, ''),
        }
      }
    }
  },

  devServer: {
    host: '0.0.0.0', // 允许任何IP地址访问
    port: 3000 // 设置端口号
  },

  compatibilityDate: '2025-05-29'
})