import axios, { type AxiosInstance } from 'axios'
import { handleError } from './handleError'

function createRequestInstance(getServerUrl: () => string): AxiosInstance {
	//获取域名
	const serverUrl = window.location.origin;
	//console.log(serverUrl);
	const instance = axios.create({
		timeout: 1000 * 60 * 5, // 超时时间
		withCredentials: true, // 允许跨域携带cookie
		baseURL: serverUrl, // 请求地址
	})

	instance.interceptors.response.use(
		async res => {
			return res
		},
		async err => {
			err = await handleError(err)
			return Promise.reject(err)
		},
	)
	return instance
}

export default createRequestInstance
