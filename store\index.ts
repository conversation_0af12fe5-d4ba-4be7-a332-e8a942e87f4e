import { defineStore, acceptHMRUpdate } from 'pinia'

export const useStore = defineStore({
	id: 'index',
	state: () => ({
		// 添加临时数据存储字段
		tempApiData: {}, // 存储API返回的临时数据
		serApiData: {}, // 存储API返回的临时数据
		isEnglish: 'zh', // 判断是否是英文
		// 友情链接相关状态
		friendshipLinks: [], // 存储友情链接原始数据
		isLinksLoaded: false, // 标识友情链接数据是否已加载
		_friendshipLinksPromise: null as Promise<any> | null, // 私有Promise缓存
	}),
	getters: {
		// 获取临时API数据的getter
		getTempApiData: state => state.tempApiData,
		// 友情链接分类数据的getter
		s_list: state => state.friendshipLinks.filter((item: any) => item.status == 1),
		d_list: state => state.friendshipLinks.filter((item: any) => item.status == 2),
		q_list: state => state.friendshipLinks.filter((item: any) => item.status == 3),
		b_list: state => state.friendshipLinks.filter((item: any) => item.status == 4),
	},
	actions: {
		// 保存API返回的临时数据
		saveTempApiData(data: any) {
			this.tempApiData = data
		},
		saveSerApiData(data: any) {
			this.serApiData = data
		},
		// 判断是否是英文
		setIsEnglish(value: string) {
			this.isEnglish = value
		},
		// 清除临时数据
		clearTempApiData() {
			this.tempApiData = {}
		},
		// 获取友情链接数据
		async getFriendshipLinks() {
			// 如果数据已经加载过，直接返回
			if (this.isLinksLoaded) {
				return
			}

			// 如果已经有正在进行的请求，直接返回该Promise
			if (this._friendshipLinksPromise) {
				return this._friendshipLinksPromise
			}

			// 创建新的Promise并缓存
			this._friendshipLinksPromise = this._loadFriendshipLinks()

			try {
				await this._friendshipLinksPromise
			} catch (error) {
				// 如果请求失败，清除缓存的Promise，允许重新尝试
				this._friendshipLinksPromise = null
				throw error
			}
		},

		// 私有方法：实际执行API调用
		async _loadFriendshipLinks() {
			try {
				// 动态导入API模块
				const { default: $api } = await import('@/service/webRequest')
				const res = await $api.get('/api/index/friendship_link')
				this.friendshipLinks = res.data.data.list
				this.isLinksLoaded = true
				console.log('友情链接数据已加载:', res.data.data)
				return res.data.data
			} catch (error) {
				console.error('获取友情链接数据失败:', error)
				throw error
			}
		},
	},
})

if (import.meta.hot) {
	import.meta.hot.accept(acceptHMRUpdate(useStore, import.meta.hot))
}
