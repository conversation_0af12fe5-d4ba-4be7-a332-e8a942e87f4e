<template>
    <!-- PC端布局 -->
    <div class="flex justify-between w-full h-full">
        <!-- 左边导航栏 -->


        <!-- 右边内容栏 -->
        <div class="rightcon">
            <div class="flex top_search ">
                <div style="width: 572px;height: 69px;margin-left: 415px;">
                    <img :src="`/img/index/dlogo.png`" alt="" style="width: 100%;height: 100%;object-fit: cover;">
                </div>
                <!-- <div class="inputform">
                    <input v-model="inputSearch" class="contactInput w-full md:w-[337px]" type="text"
                        placeholder="请输入搜索关键字" />
                    <div class="search_rinput">
                        <img src="/img/index/hmsearch.png" alt="">
                    </div>
                </div> -->
            </div>

            <div class="News_box">
                <div class="lef_box">
                    <div class="lef_boxitem1">
                        <div class="lef_boxitem1_shu"></div>
                        <div class="lef_boxitem1_title">关于我们</div>
                    </div>
                    <div :class="['lef_boxitem2',{'active': cateindex == index }]" v-for="(item, index) in newsListtyp" :key="index" @click="gotoHt(index)">
                        <div class="lef_boxitem2_title">{{ item.name }}</div>
                    </div>
                </div>
                <div class="rig_box">
                    <div class="re_box">
                        <div class="re_box_item" v-if="isShow">
                            <div class="re_title">团市委的主要职责</div>
                            <div class="re_slice"></div>
                            <div class="re_content">
                                <span>一、领导全市的共青团工作。</span>
                                <span>二、负责全市团的组织建设，积极创新基层组织制度，协助党组织管理、选拔和培训团干部，对团校、青少年活动阵地、青少年报刊等事务进行规划和管理。</span>
                                <span>三、积极向党和政府反映青少年的意愿和呼声，提出意见和建议，充分发挥民主参与和民主监督作用。</span>
                                <span>四、贯彻实施《河南省未成年人保护条例》，参与青少年事业发展规划和青少年工作政策的制定，积极实施希望工程，会同有关部门做好青少年权益保护和预防青少年犯罪工作，研究指导社区团的各项工作。</span>
                                <span>五、调查青年思想动态和青年工作情况，研究青少年工作理论、青少年思想教育、青少年事业发展等项工作，提出相应对策，开展各种活动。</span>
                                <span>六、领导全市青联、学联和少先队工作，对全市性青年社团组织实行指导和管理。</span>
                                <span>七、协助教育部门做好大、中、小学校的教育管理工作，维护学校稳定和社会安定团结。</span>
                                <span>八、会同有关部门对全市青少年外事工作实行归口管理和提供服务，负责与国外青少年团体、政府青年机构、国际地区性青年组织及其他友好团体的交流工作，负责青年对外宣传工作，负责做好青年统战对象的团结教育工作。</span>
                                <span>九、围绕全市经济工作大局，组织和带领团员青年发挥生力军和突击队作用。</span>
                                <span>十、动员组织广大团员青年积极参与创建文明城市为主的各类创建活动。</span>
                                <span>十一、围绕党政工作大局，突出青年特点，完善服务内容，促进青年创业就业。</span>
                                <span>十二、做好党政机关团员青年效能建设工作，开展各种活动，带动机关服务质量、办事效率和执行力的进一步提高。</span>
                                <span>十三、承担市委交办的其他事项。</span>
                            </div>
                        </div>
                        <div class="re_box_item" v-else>
                            <div class="re_title">团市委的机构设置</div>
                            <div class="re_slice"></div>
                            <div class="re_contents">
                                <div>
                                    <span>(一)办公室</span>
                                    <span>电话：63225071</span>
                                    <span>协调处理机关日常事务；负责团市委重要会议的会务工作；负责机关文秘、信访、保密、网络工作；负责编发内部信息、简报工作；指导全市共青团的调查研究工作；负责对外联系、机关财务、资产、行政后勤等管理工作。</span>
                                </div>
                                <div>
                                    <span>(二)组织部</span>
                                    <span>电话：63225070</span>
                                    <span>研究制定全市共青团组织、团干部和团员队伍建设的政策措施；指导推动全市团的基层组织、团员队伍和团干部队伍建设；负责团费收缴、管理和全市团的基层组织统计工作；组织开展全市性先进基层团组织和优秀团员、优秀团干部的评选表彰工作；指导推动团的基层阵地建设和基层组织信息化工作；协助党委管理县级团委的领导班子成员；协助管理在洛的团中央委员、候补委员和团省委委员、候补委员；指导全市团干部的教育培训；负责团市委机关和直属单位的人事管理、机构编制等工作；负责离退休人员的管理服务工作。</span>
                                </div>
                                <div>
                                    <span>(三)宣传部</span>
                                    <span>电话：63225062</span>
                                    <span>组织指导全市团员青年的思想道德教育和全市团组织的文化活动；加强青年理想信念教育,引导全市广大青少年践行社会主义核心价值观；指导全市团的宣传工作；指导团员青年理论学习；提供和编写团干部教育材料和团课教材；强化青少年网络思想引领工作,提升网络舆情分析和引导能力；指导青少年活动阵地建设和青少年开展健康有益的活动；负责团市委的新闻、宣传工作,抓好团的宣传队伍建设。</span>
                                </div>
                                <div>
                                    <span>(四)青年发展部</span>
                                    <span>电话：63225069</span>
                                    <span>研究制订促进全市青年发展的规划和政策措施,建立和完善青年服务体系；开展促进全市青年就业、创业、创新、创优和全市青年职业文明素养培养、技能培训提升等工作；指导洛阳市青年企业家协会、洛阳市青年农民专业合作社联合会等有关社会团体工作；组织实施对团市委定点扶贫地区的帮扶工作。</span>
                                </div>
                                <div>
                                    <span>(五)学校部(维护青少年权益部)</span>
                                    <span>电话：63225067</span>
                                    <span>组织指导全市大中专院校、中学共青团工作,了解掌握学生的思想动态,根据党的教育方针开展团的各项活动；指导青年学生开展社会实践、勤工助学和课外科技、文化活动；培养青年学生创新意识和科技创新能力,推进大学生素质教育；负责市学联的日常事务工作；指导全市少先队工作,研究少年儿童的思想品德教育,协调和配合社会有关部门调查研究,反映情况,制定政策和措施；指导少先队校外阵地建设,组织开展有利于少年儿童健康成长的文化、体育活动；负责少先队宣传工作；抓好少先队辅导员队伍建设；负责少先队洛阳市工作委员会的日常事务工作；宣传贯彻《河南省未成年人保护条例》,负责全市未成年人权益保护工作；负责预防全市青少年违法犯罪工作；研究全市有关青少年发展问题,参与制定保护青少年健康成长的法规、政策；协调处理侵害青少年权益案件；指导青少年社会化服务体系建设；指导青少年报刊工作；承担市未成年人保护委员会办公室的日常工作。</span>
                                </div>
                                <div>
                                    <span>(六)统战联络部</span>
                                    <span>电话：63225102</span>
                                    <span>负责全市青年统战工作,在各族各界青年代表人士中开展联谊和交流工作；负责市青联的日常事务及会员团体和委员的联络工作；协助管理在洛全国青联成员、省青联委员；开展青年科技人才和青年留学人员的联系服务工作；开展对台港澳地区青年及青年侨胞的统战工作；负责全市青少年外事工作,开展同国外、境外青少年组织的联络和交流活动。</span>
                                </div>
                                <div>
                                    <span>(七)社会联络部</span>
                                    <span>电话：63230538</span>
                                    <span>研究制订全市共青团、青年社会组织参与社会建设的总体规划和政策制度；开展对全市青年社会组织和新兴青年群体的联系服务和引导工作；研究制订全市青年志愿服务工作规划、意见,指导全市青年志愿服务工作；指导建设全市青少年事务社会工作专业人才队伍；协调、指导全市青少年生态环境保护工作；承担全市性青年社团组织的指导和管理工作。</span>
                                </div>
                                <div>
                                    <span>(八)机关党总支</span>
                                    <span>电话：63235856</span>
                                    <span>负责机关和直属单位的党群工作。</span>
                                </div>
                            </div>
                        </div>
                    </div>


                </div>
            </div>


            <!-- 右分享 -->
            <!-- <div style="position: absolute;top: 300px;right:36px;" class="flex flex-col fiximg">
            <img :src="`/img/index/douyin.png`" alt="">
            <img :src="`/img/index/wb.png`" alt="">
            <img :src="`/img/index/wx.png`" alt="">
            <img :src="`/img/index/blbl.png`" alt="">
        </div> -->
            <!-- 底部鼠标 -->
            <div style="position: absolute;bottom: 30px;right:30px;">
                <img :src="`/img/index/hmouse.png`" alt="">
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
// import $api from '@/service/webRequest'
const inputSearch = ref('');
const cateindex = ref(0);
let isShow = ref(true);
const newsListtyp = ref([
    {name:'团市委职责'},
    {name:'​各部门职能'}
]);
const gotoHt =(index:number)  => {
    if(index == 0){
        cateindex.value = 0;
        isShow.value = true;
    }else{
        cateindex.value = 1;
        isShow.value = false;
    }
    
}
//加载中
onMounted(() => {

})



</script>

<style lang="scss" scoped>
@import '@/assets/index.scss';


/* pc端样式 */

.rightcon {
    width:100%;
    height:100%;
    background-image: url('/img/aboutxq.png');
    background-size: 100% 100%;
    overflow-y: auto;

    .fiximg {
        img {
            margin-bottom: 20px;
        }
    }
}

.top_search {
    margin-top: 50px;
    height: 70px;
    justify-content: space-between;
    align-items: center;
}

.inputform {
    position: relative;
    width: 340px;
    margin-right: 319px;
}

.contactInput {
    width: 337px;
    height: 50px;
    background: #FFFFFF;
    border-radius: 8px;
    border: 1px solid #DEE4E8;
    padding-left: 15px;
}

.contactInput::placeholder {
    color: #999999;
}

.contactInput:focus {
    outline: none;
}

.search_rinput {
    width: 68px;
    height: 50px;
    background: #E6E6E6;
    border-radius: 0px 8px 8px 0px;
    position: absolute;
    top: 0;
    right: 0;
    display: flex;
    justify-content: center;
    align-items: center;
}

.News_box {
    display: flex;
    justify-content: space-between;
    width: 1300px;
    margin: 0 auto;
    height: 100%;
    

    .lef_box {
        width: 260px;
        height: 100%;
        margin-top: 40px;
        margin-left: 100px;

        .lef_boxitem1 {
            width: 260px;
            height: 64px;
            background: linear-gradient(0deg, #338CDE 0%, #3D92E0 100%);
            display: flex;
            align-items: center;
            justify-content: center;

            .lef_boxitem1_title {
                width: 88px;
                // height: 22px;
                font-family: Microsoft YaHei UI;
                font-weight: bold;
                font-size: 22px;
                color: #FFFEFE;
            }

            .lef_boxitem1_shu {
                width: 4px;
                height: 25px;
                background: #FFFFFF;
                margin-right: 20px;
            }
        }

        .lef_boxitem2 {
            width: 260px;
            height: 64px;
            background: #FFFFFF;
            color: #323232;
            display: flex;
            align-items: center;
            margin-top: 10px;
            cursor: pointer;

            .lef_boxitem2_title {
                width: 100%;
                // height: 19px;
                font-family: Microsoft YaHei;
                font-weight: 400;
                font-size: 20px;
                text-align: center;
            }

            &:hover {
                background: linear-gradient(0deg, rgba(51, 140, 222, 0.12) 0%, rgba(61, 146, 224, 0.12) 100%);
                color: #3A91DF;

            }
            &.active{
                background: linear-gradient(0deg, rgba(51, 140, 222, 0.12) 0%, rgba(61, 146, 224, 0.12) 100%);
                color: #3A91DF;
            }
        }
    }

    .rig_box {
        width: 921px;

        .re_box {
            width: 100%;
            height: 680px;
            margin: 0 auto;
            margin-top: 40px;
            // padding-left: 60px;

            .re_box_item {
                width: 100%;
                background: #FFFFFF;
                border: 1px solid #EEF7FF;
                padding: 30px;
                box-sizing: border-box;
                display: flex;
                flex-wrap: wrap; /* 允许换行 */
                justify-content: flex-start; /* 项目左对齐 */

                .re_slice{
                    width: 841px;
                    border-bottom: 1px dashed #BBBBBB;
                    margin-top: 40px;
                    margin-bottom: 30px;
                }
                .re_title {
                    width: 100%;
                    font-family: Microsoft YaHei UI;
                    font-weight: bold;
                    font-size: 32px;
                    color: #393939;
                    text-align: center;  
                }
                .re_content{
                    width: 100%;
                    height: 100%;
                    font-family: Microsoft YaHei UI;
                    font-weight: 400;
                    font-size: 16px;
                    color: #999999;
                    span{
                        display: inline-block;
                        line-height: 32px;
                    }
                }

                .re_contents{
                    width: 100%;
                    height: 100%;
                    font-family: Microsoft YaHei UI;
                    font-weight: 400;
                    font-size: 16px;
                    color: #999999;
                    div{
                        margin: 20px 0;
                    }
                    span{
                        display:block;
                        // line-height: 30px;
                        &:first-child{
                            font-weight: bold;
                            font-size: 17px;
                            color: #000000;
                        }
                    }
                }
               

               
            }
        }

    }
}
</style>
